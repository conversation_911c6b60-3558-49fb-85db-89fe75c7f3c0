/* eslint-disable import/no-unresolved */
import React, { useState } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"

import Button from "@/components/ui/Button"
import Input from "@/components/ui/Input"
import { useToast } from "@/hooks"

function OtherScope1Content(props) {
	const { otherScope1Rows, setOtherScope1Rows, updateOtherScope1Emission } = props
	const { t } = useTranslation("transactions")

	const [metric, setMetric] = useState("")
	const [factor, setFactor] = useState(0.0)
	const [unit, setUnit] = useState(0.0)

	const toast = useToast()

	const addOtherScope1Row = (scope1) => {
		const newOtherScope1Row = {
			metric: metric,
			factor: Number(factor),
			unit: Number(unit),
			scope1: Number(Number(scope1).toFixed(2)),
			scope2: 0,
			scope3: 0,
		}

		const newOtherScope1Rows = [...otherScope1Rows, newOtherScope1Row]

		setOtherScope1Rows([...newOtherScope1Rows])

		updateOtherScope1Emission(newOtherScope1Row, false, newOtherScope1Rows)
	}

	const calculateOtherScope1Emission = () => {
		if (metric.trim() === "") {
			toast("error", t("discription_error"))
			return
		}

		if (parseFloat(factor) <= 0 && parseFloat(unit) <= 0) {
			toast("error", t("factor_unit_greater_0_error"))
			return
		}

		let scope1 = factor * unit

		addOtherScope1Row(scope1)
	}

	const deleteOtherScope1Row = (index) => {
		let otherScope1Row = otherScope1Rows[index]
		let newOtherScope1Rows = [...otherScope1Rows]
		newOtherScope1Rows.splice(index, 1)

		setOtherScope1Rows(newOtherScope1Rows)

		updateOtherScope1Emission(otherScope1Row, true, newOtherScope1Rows)
	}

	const handleFocus = (event) => event.target.select()

	return (
		<>
			{otherScope1Rows.map((otherScope1, index) => (
				<div key={index}>
					<div className='grid grid-cols-5 gap-10 my-1 saved-emission'>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Metric")}</label>
							<div>{otherScope1.metric}</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Factor")}</label>
							<div className='flex relative'>
								<span>{otherScope1.factor}</span>
								<span className='custom-span-unit-value-save'>{t("Kg-Co2e")}</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Units")}</label>
							<div className='flex relative'>
								<span>{otherScope1.unit}</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("TotalCo2e")}</label>
							<div className='flex relative'>
								<span>{Number(otherScope1.scope1 + otherScope1.scope2 + otherScope1.scope3).toFixed(2)}</span>
								<span className='text-nowrap custom-span-unit-value-save'>{t("kg")}</span>
							</div>
						</div>

						<div className='!items-center delete-icon'>
							<span aria-hidden onClick={() => deleteOtherScope1Row(index)}>
								<RiDeleteBin6Line />
							</span>
						</div>
					</div>
					<hr />
				</div>
			))}

			<div className='grid grid-cols-4 gap-10'>
				<div>
					<Input
						label={t("OtherEmissionSources")}
						type='text'
						labelColor='text-sky-500'
						value={metric}
						handleChange={(e) => setMetric(e.target.value)}
						handleFocus={handleFocus}
					/>
				</div>

				<div>
					<Input
						label={t("Factor")}
						labelColor='text-sky-500'
						type='number'
						value={factor}
						handleChange={(e) => setFactor(e.target.value)}
						handleFocus={handleFocus}
						unit={t("Kg-Co2e")}
					/>
				</div>
				<div>
					<Input
						label={t("Units")}
						labelColor='text-sky-500'
						type='number'
						value={unit}
						handleChange={(e) => setUnit(e.target.value)}
						handleFocus={handleFocus}
						unit={t("kgs")}
					/>
				</div>
				<div className='self-end mb-1'>
					<Button title={`${t("Add")} | +`} handleClick={() => calculateOtherScope1Emission()} color='sky-500' />
				</div>
			</div>
		</>
	)
}

export default OtherScope1Content
