.cl-user-profile-card {
	margin: 0;
	background: var(--clerk-background-color, #ffffff);
	box-shadow: var(--clerk-box-shadow, 0px 2px 8px rgba(0, 0, 0, 0.2));
	border-radius: 0.5em;
	overflow: hidden;
	padding: 1.5rem 2em;
}

.cl-user-profile-logo-text {
	display: flex;
	flex: 0 0 30%;
	margin-right: 0.5rem;
	padding-top: calc(var(--clerk-padding-mod, 1) * 1.5em);
	line-height: 1.25rem;
	font-size: 1rem;
	font-weight: var(--clerk-label-font-weight, 600);
}

.cl-user-profile-logo {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex: 1;
	padding-top: calc(var(--clerk-padding-mod, 1) * 1.5em);
	padding-bottom: calc(var(--clerk-padding-mod, 1) * 1.5em);
	font-size: 1em;
	line-height: 1.25em;
	border-bottom: 2px solid #e5e5e5;
}

.btn-invite {
	background-color: #009be2;
	color: #ffffff;
	font-weight: bold;
	padding: 0.5rem 1rem 0.5rem 1rem;
	border-radius: 0.25rem;
	margin-left: auto !important;
}

.btn-invite:hover {
	background-color: #007cb6;
}

.label {
	background-color: rgb(59 130 246);
	color: white;
	font-weight: bold;
	padding: 0.125rem 0.5rem 0.125rem 0.5rem; /* 2px */
	border-radius: 1rem;
	font-size: small;
	white-space: nowrap;
}

.btn-revoke {
	border: 2px solid #f89fa2;
	color: #f89fa2;
	font-weight: bold;
	padding: 0.25rem 1rem 0.25rem 1rem;
	border-radius: 0.5rem;
}

label.e-float-text {
	color: #0284c7;
}

.dotted-line {
	border-bottom: 3px solid #dfdfe5;
	border-style: dotted;
}

.member-img {
	background-color: #e4b4b4;
	margin-right: 0.5rem;
	min-width: 3rem;
	min-height: 3rem;
	border-radius: 50%;
	display: flex;
	align-items: center;
	/* text-align: center; */
	justify-content: center;
}

@media (max-width: 1300px) {
	.cl-component.cl-user-profile {
		flex-direction: column;
	}

	.cl-component.cl-user-profile .cl-navbar {
		margin-top: 0 !important;
		flex-direction: row !important;
		min-height: 3em !important;
	}

	.cl-component.cl-user-profile .cl-navbar-link {
		padding: calc(var(--clerk-padding-mod, 1) * 0.75rem) calc(var(--clerk-padding-mod, 1) * 2rem);
		width: auto;
		flex-grow: 1;
		border-left: none;
		border-bottom: 4px solid #e5e5e5;
		border-radius: 0px !important;
		text-align: center;
	}

	.cl-component.cl-user-profile .cl-navbar-link.cl-active {
		color: var(--clerk-primary) !important;
		background-color: var(--clerk-primary-l2) !important;
		border-left: none !important;
		border-bottom: 4px solid var(--clerk-primary) !important;
		border-radius: 0px !important;
	}
}
