/* eslint-disable no-mixed-spaces-and-tabs */

import "../../index.css"
//import { TransactionsEditFlagDialog } from "./ContextMenuComponents/TransactionsEditFlagDialog"
import { <PERSON><PERSON><PERSON>, <PERSON>10n } from "@syncfusion/ej2-base"
import {
	GridComponent,
	ColumnsDirective,
	ColumnDirective,
	// AggregatesDirective,
	// AggregateDirective,
	// AggregateColumnsDirective,
	// AggregateColumnDirective,
	Page,
	Selection,
	ContextMenu,
	Toolbar,
	Edit,
	Inject,
	Filter,
	Group,
	Resize,
	DetailRow,
	Sort,
	Aggregate,
	InfiniteScroll,
	ExcelExport,
} from "@syncfusion/ej2-react-grids"
import { useQueryClient } from "@tanstack/react-query"
import React, { useState, useEffect, useRef, useCallback } from "react"
import { useTranslation } from "react-i18next"
import { FiX } from "react-icons/fi"
import { useSelector } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
import { v1 as uuidv1 } from "uuid"

import blue_flag from "../../assets/blue_flag.png"
import green_flag from "../../assets/green_flag.png"
import lightGreen_flag from "../../assets/lightGreen_flag.png"
import orange_flag from "../../assets/orange_flag.png"
import red_flag from "../../assets/red_flag.png"
import AddReportingEntityModal from "../../components/AddReportingEntityModal.component"
import Alert from "../../components/Alert.component"
import Spinner from "../../components/ui/Spinner"
import { useToast, useGetNaceQuery, useGetSuppliers, useGetTransactions, useGetSuppliersList } from "../../hooks"
//import { useRealmApp } from "../../realm/RealmAppProvider"
//import { useGraphqlClient } from "../../graphql/realmApolloClient"
import { useRealmApp } from "../../realm/RealmAppProvider"
import useFullPageLoader from "../loader/useFullPageLoader"

import TransactionsEditAccountIdDialog from "./ContextMenuComponents/TransactionEditAccountIdDialog"
import UpdateMultipleTransactions from "./ContextMenuComponents/UpdateMultipleTransactions"
import TransactionDetailsDrawer from "./editdetails/TransactionDetailsDrawer"
import Filters from "./Filters"
import { TransactionsChildGridDialogForm } from "./TransactionsChildGridDialogForm"
import { TransactionsEditDialogForm } from "./TransactionsEditDialogForm"

/*
reference:
https://ej2.syncfusion.com/react/demos/#/material/grid/dialog-template
https://ej2.syncfusion.com/react/demos/#/material/grid/custom-binding
https://ej2.syncfusion.com/react/demos/#/material/grid/filter-menu
*/

function Transactions(props) {
	// Get the netlify ID(it will be used to fetch transactions)
	const currentOrganization = useSelector((state) => state.user.currentOrganization)

	const isCompanyRegistered = useSelector((state) => state.company)

	const netlifyID = currentOrganization ? currentOrganization.netlifyID : ""
	const RegistrationNumber = currentOrganization ? currentOrganization.RegistrationNumber : ""
	const queryClient = useQueryClient()
	const app = useRealmApp()

	const location = useLocation()
	const navigate = useNavigate()
	const { t, i18n } = useTranslation(["transactions", "common"])
	const [transactionData, setTransactionData] = useState([])
	const [allTransactions, setAllTransactions] = useState([])

	const [lines, setLines] = useState([])
	const [emissionSum, setEmissionSum] = useState(0)
	const [loader, showLoader, hideLoader] = useFullPageLoader()
	const [transactionsForEditDetails, setTransactionsForEditDetails] = useState({})
	const [showMultiTransactionsEditDialog, setShowMultiTransactionsEditDialog] = useState(false)
	const [showEditDetailsDialog, setShowEditDetailsDialog] = useState(false)
	const [showEditAccountIDDialog, setShowEditAccountIDDialog] = useState(false)
	const [openFiltersModal, setOpenFiltersModal] = useState(false)
	const [projects, setProjects] = useState([])
	const [departments, setDepartments] = useState([])
	const [showAlert, setShowAlert] = useState(false)
	const [multiData, setMultiData] = useState({})
	const reportingYear = useSelector((state) => state.reporting)
	const Toast = useToast()

	const isTransactionFetched = useRef(false)

	const isAddEmployeeTransaction = useRef(false)

	const transactionQueryInput = {
		RegistrationNumber: RegistrationNumber,
		year: reportingYear,
	}

	/* eslint react-hooks/exhaustive-deps: 0 */
	let scopeType = location.pathname.substring(location.pathname.lastIndexOf("/") + 1)
	if (scopeType) {
		if (scopeType === "scope1") {
			transactionQueryInput.Scope = 1
		} else if (scopeType === "scope2") {
			transactionQueryInput.Scope = 2
		} else if (scopeType === "scope3") {
			transactionQueryInput.Scope = 3
		}
	}

	const transactions = useGetTransactions(app, transactionQueryInput)
	const suppliersData = useGetSuppliers(app, { RegistrationNumber, year: reportingYear })
	const suppliersList = useGetSuppliersList(app, { RegistrationNumber })
	const naceData = useGetNaceQuery(app)

	const gridRef = React.useRef(null)

	/* This function will set data to edit multiple 
        Transaction like sum scope 1,2,3 and also set scope
        and also sum up their amounts etc*/
	const setDataForMultiTransactionEdit = () => {
		const data = gridRef.current.getSelectedRecords()
		let transactionIds = data
			?.map((transaction) => {
				return transaction?.TransactionID
			})
			.join(",")
		let _idData = data?.map((transaction) => {
			return transaction?._id
		})
		let _Ids = _idData.join(",")
		let totalScope = 0
		let scope1 = 0
		let scope2 = 0
		let scope3 = 0
		let SupplierID = ""
		let TransactionDate = ""
		let RegistrationNumber = ""
		let Amount = 0
		for (let i = 0; i < data.length; i++) {
			totalScope += data[i].TotalScope
			scope1 += data[i].Scope_1
			scope2 += data[i].Scope_2
			scope3 += data[i].Scope_3
			if (data[i].Amount !== "") {
				Amount += data[i].Amount
			}
			if (data[i].TransactionDate && TransactionDate === "") {
				TransactionDate = data[i].TransactionDate
			}
			if (data[i].SupplierID !== "" && SupplierID === "") {
				SupplierID = data[i].SupplierID
			}
			if (data[i].RegistrationNumber !== "" && RegistrationNumber === "") {
				RegistrationNumber = data[i].RegistrationNumber
			}
		}
		scope1 = ((scope1 || 0) * 1000).toFixed(2)
		scope2 = ((scope2 || 0) * 1000).toFixed(2)
		scope3 = ((scope3 || 0) * 1000).toFixed(2)

		let supplierFactor = 0

		let Scope3Catagory = [
			{ value: -1, label: "-" },
			{ value: 1, label: "Cat. 1 - Goods & Services" },
			{ value: 2, label: "Cat. 2 - Capital goods" },
			{ value: 3, label: "Cat. 3 - Fuel & Energy related activities" },
			{ value: 4, label: "Cat. 4 - Transport & Distribution" },
			{ value: 5, label: "Cat. 5 - Waste" },
			{ value: 6, label: "Cat. 6 - Business travel" },
			{ value: 7, label: "Cat 7. - Workers commute" },
			{ value: 8, label: "Cat. 8 - Leasing" },
		]
		let defaultScope3Catagory = { value: -1, label: "-" }
		setMultiData({
			transactionIds,
			NaceCode: "",
			Industry: "",
			Amount,
			totalScope,
			scope1,
			scope2,
			scope3,
			supplierFactor,
			scope: data[0]?.Scope,
			Status: data[0]?.Status,
			Notes: data[0]?.Notes,
			TransactionDate,
			RegistrationNumber,
			SupplierID,
			industryFactor: "",
			defaultScope3Catagory,
			Scope3Catagory,
			_Ids,
		})
		setShowMultiTransactionsEditDialog(true)
	}

	const editDialogTemplate = (props) => {
		// Open dialog box to edit or add row.
		// setTransactionsForEditDetails(props);
		return (
			<TransactionsEditDialogForm
				{...props}
				t={t}
				//setShowEditDetailsDialog={setShowEditDetailsDialog}
				//setTransactionsForEditDetails={setTransactionsForEditDetails}
				naceData={naceData.data.data || []}
				currentYear={reportingYear}
				navigate={navigate}
				// history={history}
				scopeType={scopeType}
				openEditDetailsModal={openEditDetailsModal}
				// setIsEditing={setIsEditing}
				supplierData={suppliersList?.data?.data || []}
				reportingYear={reportingYear}
			/>
		)
	}

	const childGridDialogTemplate = (props) => {
		return <TransactionsChildGridDialogForm {...props} t={t} />
	}

	// Options for Project dropdown
	// const projectOptions = [
	// 	{ text: "Project A", value: "Project A" },
	// 	{ text: "Project B", value: "Project B" },
	// 	{ text: "Project C", value: "Project C" },
	// ]

	// // Options for Department dropdown
	// const departmentOptions = [
	// 	{ text: "HR", value: "HR" },
	// 	{ text: "Finance", value: "Finance" },
	// 	{ text: "Engineering", value: "Engineering" },
	// ]

	// ------------------------------ Syncfusion react grid configuration. ---------------------------------
	const toolbarOptions = [
		{ text: "Edit", prefixIcon: "e-edit", id: "edit123", tooltipText: "Edit" },
		{ text: "Add", prefixIcon: "e-add", id: "add123", tooltipText: "Add" },
		{ text: "Excel Export", prefixIcon: "e-excelexport", id: "excel123", tooltipText: "Export to Excel" },
		{ text: "Copy", prefixIcon: "e-copy", id: "copy321", tooltipText: "Copy" },
		{ text: "Filter", prefixIcon: "e-filter", id: "filter123", tooltipText: "Filter" },
	]

	L10n.load({
		en: {
			grid: {
				Edit: "Edit",
				Add: "Add",
				ExcelExport: "Excel Export",
				Copy: "Copy",
				AddRecord: "Add Record",
				SaveButton: "Save",
				CancelButton: "Cancel",
			},
		},
		no: {
			grid: {
				Edit: "Rediger",
				Add: "Legg til",
				ExcelExport: "Excel Eksport",
				Copy: "Kopier",
				AddRecord: "Legg til ny post",
				SaveButton: "Lagre",
				CancelButton: "Avbryt",
			},
		},
		// Add translations for other languages if needed
	})

	const handleFilters = (filters) => {
		let filtered = []

		// here filtered is array of transactions each transaction contain Analysis data
		// which is array of object each object contain analysisID and share

		// selectedDepartments and selectedProjects are array of object each object contain value and label
		// now we need to filter transactions based on selectedDepartments and selectedProjects

		// if selectedDepartments and selectedProjects are empty then we should show all transactions
		// otherwise we should filter transactions based on selectedDepartments and selectedProjects

		if (filters.length == 0) {
			setTransactionData(allTransactions)
			return
		}

		// make object of selectedDepartments and selectedProjects for easy access where key will
		// be value and value will be lable

		let map = {}

		filters.forEach((department) => {
			map[department.value] = department.label
		})

		// selectedProjects.forEach((project) => {
		// 	map[project.value] = project.label
		// })

		// if selectedDepartments and selectedProjects are not empty then we should filter transactions based
		// on selectedDepartments and selectedProjects

		// instead of filter use foreach loop

		allTransactions.forEach((transaction) => {
			if (transaction?.Analysis && transaction?.Analysis.length > 0) {
				let s = transaction?.Analysis?.some((analysis) => {
					return analysis.AnalysisID in map
				})

				if (s) {
					filtered.push(transaction)
				}
			}
		})

		setTransactionData(filtered)
	}

	const selectionsettings = {
		mode: "Row",
		type: "Multiple",
		persistSelection: true,
		checkboxOnly: true,
	}

	const contextMenuItems = [
		{ text: t("update_multiple_transactions"), id: "edit" },
		{ text: t("update_account_id"), id: "update_accountid" },
		// { text: "Run function", id: "run_function" },
		{ text: t("delete_records"), id: "delete" },
	]
	const editSettings = {
		allowDeleting: true,
		allowAdding: RegistrationNumber ? true : false,
		allowEditing: true,
		mode: "Dialog",
		template: editDialogTemplate,
	}
	const pageSettings = { pageCount: 5, pageSize: 30 }
	const filterSettings = {
		type: "Excel",
		columns: location?.state?.filter
			? [
					{
						field: "Scope_3_Category",
						matchCase: false,
						operator: "equal",
						value: Number(location?.state?.catagory),
					},
			  ]
			: location?.state?.supplierFIlter
			? [
					{
						field: "SupplierName",
						matchCase: false,
						operator: "equal",
						value: location?.state?.supplierName,
					},
			  ]
			: [],
	}

	// Show images of flag for status.
	const imageTemplate = (props) => {
		const statusData = {
			0: blue_flag,
			1: green_flag,
			2: orange_flag,
			3: red_flag,
			4: lightGreen_flag,
		}
		const displayNameForFlag = {
			0: "Info",
			1: "Ok - Activity data",
			2: "Processed",
			3: "Warning",
			4: "Checked",
		}
		return (
			<div className='flex items-center'>
				<img src={statusData[props.Status]} alt={props.Status} style={{ marginTop: "0px", height: "20px" }} />
				{displayNameForFlag[props.Status]}
			</div>
		)
	}

	// Show summation of emission at the footer of emission solumn.
	// const footerEmissionSum = (props) => {
	// 	return <span>Sum: {parseFloat(emissionSum).toFixed(3)} kg/co2e</span>
	// }

	const updateCache = async (Scopes) => {
		try {
			const queries = Scopes.map((scope) => {
				if (scope === -1) {
					return {
						queryKey: ["getTransactions", { RegistrationNumber, year: reportingYear }],
						queryFn: () => app.getTransactions({ RegistrationNumber, year: reportingYear }),
					}
				} else {
					return {
						queryKey: ["getTransactions", { RegistrationNumber, Scope: scope, year: reportingYear }],
						queryFn: () => app.getTransactions({ RegistrationNumber, Scope: scope, year: reportingYear }),
					}
				}
			})
			await Promise.all(queries.map((query) => queryClient.prefetchQuery(query)))
		} catch (error) {
			console.error(error)
		}
	}

	const updateTransactionInGrid = (data) => {
		try {
			let newTransactionData = transactionData.map((transaction) => {
				const updatedTransaction = data.find((res) => res._id.toString() === transaction._id.toString())
				return updatedTransaction
					? {
							...transaction,
							...updatedTransaction,
							TotalScope: (updatedTransaction.Scope_1 + updatedTransaction.Scope_2 + updatedTransaction.Scope_3) * 1000,
					  }
					: transaction
			})

			setTransactionData(newTransactionData)

			// set all transactions to new transaction data as well so that
			// when filters applied on grid then we should have all transactions as well
			setAllTransactions(newTransactionData)
		} catch (error) {
			// handle error
		}
	}

	const getScopeType = () => {
		let scopeType = location.pathname.substring(location.pathname.lastIndexOf("/") + 1)
		let scopeTypeValue = 0
		if (scopeType) {
			if (scopeType === "scope1") {
				scopeTypeValue = 1
			} else if (scopeType === "scope2") {
				scopeTypeValue = 2
			} else if (scopeType === "scope3") {
				scopeTypeValue = 3
			}
		}

		return scopeTypeValue
	}

	// Update Account Id Of Transaction
	const updateAccountID = async (accountID, accountDescription) => {
		try {
			showLoader()

			const selectedRecords = gridRef.current.getSelectedRecords()

			let ids = []

			// get ids of selected records
			selectedRecords.forEach((record) => {
				ids.push(record._id.toString())
			})

			let sendData = {
				AccountID: accountID,
				AccountDescription: accountDescription,
				ids,
			}

			const result = await app.updateAccountID(sendData)

			if (result && result.length) {
				let scopeTypeValue = getScopeType()

				// if scope of updated transaction changes then we should delete that transaction from grid

				if (scopeTypeValue && scopeTypeValue.toString() != result[0].Scope.toString()) {
					result.forEach((transaction) => {
						gridRef.current.deleteRecord({ _id: transaction._id })
					})
					const { Scope } = result[0]
					updateCache([Scope])
				} else if (scopeTypeValue && scopeTypeValue.toString() == result[0].Scope.toString()) {
					updateTransactionInGrid(result)
				}

				Toast("success", t("account_id_updated_message"))

				gridRef.current.clearSelection()
				suppliersData.refetch()
				hideLoader()
			} else {
				Toast("error", t("error_updating_account_id_message"))
				hideLoader()
			}
		} catch (error) {
			hideLoader()
			Toast("error", t("error_updating_account_id_message"))
		}
	}

	const removeNullAndEmptyArrayFromScopeData = (scopeData) => {
		try {
			// Create a new object to store non-null and non-empty values
			const cleanedData = {}

			// Iterate through all properties of scopeData
			for (const key in scopeData) {
				// Skip if property doesn't exist
				//if (!scopeData.hasOwnProperty(key)) continue

				const value = scopeData[key]

				// Check if value is an array
				if (Array.isArray(value)) {
					// Only keep non-empty arrays
					if (value.length > 0) {
						cleanedData[key] = value
					}
				}
				// Check if value is null or undefined
				else if (value !== null && value !== undefined) {
					cleanedData[key] = value
				}
			}

			return cleanedData
		} catch (error) {
			console.log(error)
			return scopeData // Return original data if error occurs
		}
	}

	//Update Scope (1, 2, 3) Value On Edit Details
	const updateScopeValue = async (id, Data, isNewTransaction = false, isCanceled = false) => {
		// Early return if the transaction is canceled

		const scopeData = removeNullAndEmptyArrayFromScopeData(Data)

		try {
			if (isCanceled) {
				// if new transaction is canceled then remove that transaction from grid

				if (isNewTransaction) {
					let transactions = gridRef.current.dataSource

					transactions.splice(0, 1)
					gridRef.current.dataSource = transactions
					gridRef.current.refresh()
				}
				return
			}

			let { Description } = scopeData

			if (typeof Description == "string") {
				Description = Description.split(",")
			} else {
				if (Description.length === 0) {
					Description = [""]
				}
			}

			let {
				Scope_1,
				Scope_2,
				Scope_3,
				Scope_3_Category,
				Scope,
				Period,
				PeriodYear,
				mobileCombustion,
				renewable_energy,
				non_renewable_energy,
				stationaryCombustion,
				nuclear,
				kwh,
				scope1CombustionRows,
				scope1VolumeRows,
				scope1VehicleRows,
				scope1FugitiveRows,
				scope1OtherRows,
				scope2ElectricityRows,
				scope2HeatRows,
				scope2OtherEmissionRows,
				scope2ElectricTransportRows,
			} = Data

			if (!Scope_3_Category || typeof Scope_3_Category === "string") {
				Scope_3_Category = 3
			}

			// Prepare the data to update
			const updateData = {
				Scope_1: Scope_1 / 1000,
				Scope_2: Scope_2 / 1000,
				Scope_3: Scope_3 / 1000,
				Scope_3_Category,
				Scope,
				Period: Period.toString(),
				PeriodYear,
				mobileCombustion: mobileCombustion,
				renewable_energy: renewable_energy,
				non_renewable_energy: non_renewable_energy,
				stationaryCombustion: stationaryCombustion,
				nuclear: nuclear,
				kwh: kwh,
				Description,
				scope1CombustionRows,
				scope1VolumeRows,
				scope1VehicleRows,
				scope1FugitiveRows,
				scope1OtherRows,
				scope2ElectricityRows,
				scope2HeatRows,
				scope2OtherEmissionRows,
				scope2ElectricTransportRows,
			}

			const filteredData = removeNullAndEmptyArrayFromScopeData(updateData)

			const isEmploeeCommutingTransaction = scopeData?.isEmploeeCommutingTransaction

			// If it's a new transaction, add it and return
			if (isNewTransaction) {
				const data = { ...scopeData, ...updateData, Status: 1 }
				;[
					"scope1",
					"scope2",
					"scope3",
					"scope",
					"renewable",
					"TotalScope",
					"category",
					"isNewTransaction",
					"isEmploeeCommutingTransaction",
				].forEach((key) => delete data[key])
				setShowEditDetailsDialog(false)
				addNewTransaction(data, isEmploeeCommutingTransaction)
				return
			}

			// If no ID is provided, use the ID from the transactions for edit details
			if (!id) id = transactionsForEditDetails._id.toString()

			// Add the status to the update data
			updateData.Status = 1 // Status default value for edited transaction is 1(green flag)

			// Add the market, location, and consumption based data if they are not zero
			;["marketBased", "locationBased", "consumptionBased"].forEach((key) => {
				updateData[key] = scopeData[key]
			})

			// Prepare the data to send
			const dataToSend = {
				transactionId: id.toString(),
				transaction: filteredData,
			}

			// Determine the scope type value from the scope type.
			const scopeTypeValue = scopeType === "scope1" ? 1 : scopeType === "scope2" ? 2 : scopeType === "scope3" ? 3 : 3

			showLoader()

			// Update the transaction scope

			const result = await app.updateTransactionScope({ ...dataToSend })

			setShowEditDetailsDialog(false)

			if (result.status === "done") {
				const transactionOutputData = result.transaction
				const index = transactionData.findIndex((transaction) => transaction._id.toString() === id.toString())

				if (index > -1) {
					const newTransactionData = [...transactionData]
					;["Scope_1", "Scope_2", "Scope_3", "Period", "Status", "Scope", "PeriodYear", "Scope_3_Category"].forEach(
						(key) => {
							if (transactionOutputData[key]) newTransactionData[index][key] = transactionOutputData[key]
						}
					)

					const totalScope = newTransactionData[index].TotalScope
					const emissionSumTemp = emissionSum - totalScope
					newTransactionData[index] = { ...newTransactionData[index], ... }
					newTransactionData[index].Scope_1 = transactionOutputData.Scope_1
					newTransactionData[index].Scope_2 = transactionOutputData.Scope_2
					newTransactionData[index].Scope_3 = transactionOutputData.Scope_3
					newTransactionData[index].TotalScope = (
						(transactionOutputData.Scope_1 + transactionOutputData.Scope_2 + transactionOutputData.Scope_3) *
						1000
					).toFixed(2)
					newTransactionData[index].Description = Description

					setEmissionSum(emissionSumTemp + newTransactionData[index].TotalScope)
					setTransactionData(newTransactionData)

					gridRef.current.dataSource = newTransactionData

					gridRef.current.refresh()
					// set all transactions to new transaction data as well so that
					// when filters applied on grid then we should have all transactions as well
					setAllTransactions(newTransactionData)
				}

				if (scopeTypeValue !== transactionOutputData.Scope) updateCache([transactionOutputData.Scope])

				suppliersData.refetch()
				hideLoader()
				Toast("success", t("emission_updated_message"))
			} else {
				hideLoader()
				Toast("error", t("error_updating_message"))
			}
		} catch (error) {
			console.log(error)
			Toast("error", t("error_updating_message"))
		}
	}

	const addNewTransaction = async (data, isEmploeeCommutingTransaction) => {
		try {
			let newData = { ...data }

			if (!newData.Analyis) {
				newData["Analyis"] = []
			}

			// if supplier is not selected
			// then we should delete supplirId, supplier factor and nace code

			if (data.SupplierName === "Add without supplier") newData.SupplierName = ""

			if (data.SupplierID === "") {
				delete newData.SupplierID
				delete newData.supplierFactor
				delete newData.NaceCode
			}

			showLoader()

			const result = await app.addNewTransaction({ RegistrationNumber, data: newData })

			hideLoader()

			let newTransactions = [...gridRef.current.dataSource]

			if (result.success) {
				newData._id = result.transaction._id
				let scopeType = location.pathname.substring(location.pathname.lastIndexOf("/") + 1)
				let scopeTypeValue = 4
				if (scopeType) {
					if (scopeType === "scope1") {
						scopeTypeValue = 1
					} else if (scopeType === "scope2") {
						scopeTypeValue = 2
					} else if (scopeType === "scope3") {
						scopeTypeValue = 3
					}
				}

				if (scopeTypeValue == newData["Scope"] || !newData["Scope"] || scopeTypeValue == 4) {
					let newTransaction = {}

					newTransaction = { ...newData }
					newTransaction.Scope_1 = newData.Scope_1
					newTransaction.Scope_2 = newData.Scope_2
					newTransaction.Scope_3 = newData.Scope_3
					newTransaction.TotalScope = ((newData.Scope_1 + newData.Scope_2 + newData.Scope_3) * 1000).toFixed(2)

					// update top transaction because it automatically add transaction at the top
					// but few properties are missing so we need to replace top transaction with new transaction

					if (!isEmploeeCommutingTransaction) newTransactions.splice(0, 1, newTransaction)
					else newTransactions.unshift(newTransaction)

					// Update the grid's dataSource with the new array
					gridRef.current.dataSource = newTransactions

					// Refresh the grid to reflect the changes
					gridRef.current.refresh()

					setTransactionData(newTransactions)

					// set all transactions to new transaction data as well so that
					// when filters applied on grid then we should have all transactions as well
					setAllTransactions(newTransactions)
					Toast("success", t("transaction_added_message"))
				} else {
					// let suppose we are in scope 1 and we add transaction of scope 2
					// then we need to delete transaction from the grid because it automatically
					// adds one

					newTransactions.splice(0, 1)

					// Update the grid's dataSource with the new array
					gridRef.current.dataSource = newTransactions

					// Refresh the grid to reflect the changes
					gridRef.current.refresh()

					updateCache([newData["Scope"]])

					Toast("success", t("transaction_added_message"))
				}
				suppliersData.refetch()
			} else {
				newTransactions.splice(0, 1)

				// Update the grid's dataSource with the new array
				gridRef.current.dataSource = newTransactions

				// Refresh the grid to reflect the changes
				gridRef.current.refresh()

				setTransactionData(newTransactions)

				// set all transactions to new transaction data as well so that
				// when filters applied on grid then we should have all transactions as well
				setAllTransactions(newTransactions)
				Toast("error", t("error_adding_transaction_message"))
				hideLoader()
			}
		} catch (error) {
			console.log(error)
			Toast("error", t("error_adding_transaction_message"))
		}
	}

	const deleteRecords = async () => {
		setShowAlert(false)
		const selectedrecords = gridRef.current.getSelectedRecords()
		let ids = []
		selectedrecords.forEach((record) => {
			ids.push(record._id.toString())
		})

		showLoader()

		const result = await app.deleteTransactions({ RegistrationNumber, ids })

		if (result.success) {
			let newTransactionData = [...transactionData]
			ids.forEach((id) => {
				//gridRef.current.deleteRecord({ _id: id })
				const index = newTransactionData.findIndex((transaction) => id.toString() === transaction._id.toString())
				if (index > -1) {
					newTransactionData.splice(index, 1)
				}
			})
			setTransactionData(newTransactionData)
			// set all transactions to new transaction data as well so that
			// when filters applied on grid then we should have all transactions as well
			setAllTransactions(newTransactionData)
			if (scopeType !== "scope1" && scopeType !== "scope2" && scopeType !== "scope3") updateCache([1, 2, 3, -1])
			else updateCache([-1])
			suppliersData.refetch()
			hideLoader()
			Toast("success", t("transactions_deleted_message"))
		} else {
			Toast("error", t("error_deleting_transactions_message"))
		}
	}

	const contextMenuClick = async (args) => {
		if (gridRef && gridRef.current) {
			/** Get the selected records. */
			const selectedrecords = gridRef.current.getSelectedRecords()
			let ids = []
			let ids_No_Amount = []
			selectedrecords.forEach((record) => {
				ids.push(record._id)
				if (!record.Amount) {
					ids_No_Amount.push(record._id)
				}
			})

			if (args.item.id === "edit") {
				setDataForMultiTransactionEdit()
			} else if (args.item.id === "delete") {
				setShowAlert(true)
			} else if (args.item.id === "update_accountid") {
				if (ids_No_Amount.length > 0) {
					Toast("error", t("transaction_amount_missing_message"))
				} else {
					setShowEditAccountIDDialog(true)
				}
			}
		}
		return
	}

	const copyData = (args) => {
		if (args.id === "add123") {
			gridRef.current?.addRecord()
			return
		}

		if (args.id === "edit123") {
			gridRef.current?.startEdit()
			return
		}

		if (args.id == "filter123") {
			setOpenFiltersModal(true)
			return
		}

		if (gridRef.current.getSelectedRecords().length > 0) {
			if (args.id == "copy321") {
				gridRef.current.copy(true)
				Toast("success", t("rows_copied_message"))
			}

			if (args.id === "excel123") {
				const selectedRecords = gridRef.current.getSelectedRecords()

				const exportProperties = {
					dataSource: selectedRecords,
				}
				gridRef.current.excelExport(exportProperties)
			}
		} else {
			Toast("error", t("no_transaction_selected_message"))
		}
	}

	const openEditDetailsModal = (data) => {
		delete data.supplierData
		delete data.naceShortName
		delete data.naceDescription
		delete data.customEmmisonFactor
		delete data.SupplierValue
		delete data.changedScopeValue
		delete data.history
		delete data.naceData
		delete data.defaultScope3Catagory
		delete data.Scope3Catagory
		delete data.Scope_1C
		delete data.Scope_2C
		delete data.Scope_3C
		delete data.TotalScope

		showLoader()

		setTransactionsForEditDetails({ ...data })
		setShowEditDetailsDialog(true)
		hideLoader()
	}

	// Whenever some edit/add process is done this fuction will be called.
	const actionComplete = async (args) => {
		// request type will be given argument which show which type of action user performed like saved edited row, started editing row, etc..

		if (args.requestType === "add") {
			const dialog = args.dialog
			if (dialog) {
				dialog.header = t("addTransactionHeading")
			}
		}

		if (args.requestType === "beginEdit" || args.requestType === "add") {
			if (Browser.isDevice) {
				args.dialog.height = window.innerHeight - 90 + "px"
				args.dialog.dataBind()
			}
		}
		// On any edit in the grid, update the suppliers in the database.
		// "args" containes all the necessary data regarding edit
		if (args.action === "edit" && args.requestType === "save") {
			// find the index 0f row which was edited.
			let index = transactionData.findIndex((transaction) => transaction._id.toString() === args.data._id.toString())
			if (index !== -1) {
				// Revert data to pre-edit state. show edited data in grid only after successful updation in database,
				let newTransactionData = transactionData
				newTransactionData[index] = args.previousData
				setTransactionData(newTransactionData)

				setAllTransactions(newTransactionData)
				// Create new transaction object with new data
				let oldData = transactionData[index]
				let newData = {}
				if (args.data.SupplierID && args.data.SupplierID !== "" && args.data.SupplierID !== "dummy") {
					newData.SupplierID = args.data.SupplierID ? args.data.SupplierID : ""
					newData.SupplierName = args.data.SupplierName ? args.data.SupplierName : ""

					//Toast("error", "Supplier can not be empty")
				}
				if (typeof args.data.Description === "string") {
					args.data.Description = args.data.Description.split(",")
				}
				// Adding all the necessary properties to update in database.

				newData.TransactionID = args.data.TransactionID ? args.data.TransactionID : ""
				newData.NaceCode = args.data.NaceCode ? args.data.NaceCode : ""
				newData.Notes = args.data.Notes ? args.data.Notes : ""
				newData.Description = args.data.Description ? args.data.Description : []

				newData.Scope_1 = (args.data.Scope_1 ? args.data.Scope_1 : 0) / 1000.0
				newData.Scope_2 = (args.data.Scope_2 ? args.data.Scope_2 : 0) / 1000.0
				newData.Scope_3 = (args.data.Scope_3 ? args.data.Scope_3 : 0) / 1000.0
				newData["Scope_3_Category"] = Number(args?.data?.Scope_3_Catagory)
				newData["RegistrationNumber"] = args?.data?.RegistrationNumber
				newData["TransactionDate"] = args?.data?.TransactionDate
				newData["supplierFactor"] = args.data.SupplierFactor || 0
				newData["marketBased"] = (args.data.Scope_2 ? args.data.Scope_2 : 0) / 1000.0

				newData.Status = args.data.Status || args.data.Status == 0 ? parseInt(args.data.Status) : 2 // Status default value is 2(orange flag)

				if (args?.data?.customFactor !== "-1") {
					newData["NaceCode"] = `Custom factor is ${Number(args?.data?.customFactor)}`
				}

				showLoader()

				const result = await app.updateTransaction({ ...newData, _id: args.data._id.toString() })

				hideLoader()

				if (result.status === "done") {
					newData.isEdited = true
					newData["Scope_3_Category"] = Number(args?.data?.Scope_3_Catagory)
					newData["Scope_1"] = result.Scope_1
					newData["Scope_2"] = result.Scope_2
					newData["Scope_3"] = result.Scope_3
					//newData = { ...oldData, ...newData }
					let newTransactionData = transactionData
					let updatedTransaction = { ...oldData, ...newData }
					let emissionSumTemp = emissionSum
					let totalScope = updatedTransaction.TotalScope
					emissionSumTemp = emissionSumTemp - totalScope

					updatedTransaction.TotalScope = (newData.Scope_1 + newData.Scope_2 + newData.Scope_3) * 1000

					//newTransactionData[index].SupplierName = newData.SupplierName
					//newTransactionData[index].supplierFactor = newData.supplierFactor
					//newTransactionData[index].SupplierID = newData.SupplierID
					//newTransactionData[index].Status = newData.Status

					emissionSumTemp += updatedTransaction.TotalScope

					setEmissionSum(emissionSumTemp)

					newTransactionData.splice(index, 1, updatedTransaction)

					setTransactionData(newTransactionData)

					setAllTransactions(newTransactionData)

					gridRef.current.refresh()

					Toast("success", t("transaction_updated_message"))
					suppliersData.refetch()

					//updateSupplierCache()
					//updateCache([transactionQueryInput?.Scope])
				} else {
					// Handle error
					Toast("error", t("error_updating_transaction_message"))
					hideLoader()
				}
			}
		}

		if (args.action === "add" && args.requestType === "save") {
			const newTransactions = [...gridRef.current.dataSource]

			// Initialize new transaction data
			let newData = {
				SupplierName: args.data.SupplierName || "",
				SupplierID: args.data.Supplier || "",
				supplierFactor: Number(args.data.SupplierFactor) || 19,
				Scope: args.data.Scope ? Number(args.data.Scope) : "",
				TransactionDate: args.data.TransactionDate || "",
				Relation: args.data.Relation || "",
				Scope_1: (Number(args.data.Scope_1) || 0) / 1000.0,
				Scope_2: (Number(args.data.Scope_2) || 0) / 1000.0,
				Scope_3: (Number(args.data.Scope_3) || 0) / 1000.0,
				marketBased: (Number(args.data.Scope_2) || 0) / 1000.0,
				TransactionID: args.data.TransactionID || "",
				NaceCode: args.data.NaceCode || "",
				Notes: args.data.Notes || "",
				Amount: Number(args.data.Amount) || 0,
				AccountID: "0",
				AccountDescription: "NA",
				Lines: [],
				Status: args.data.Status !== undefined && args.data.Status !== null ? parseInt(args.data.Status) : 2, // Status default value is 2(orange flag)
				Scope_3_Category: parseInt(args.data.Scope_3_Category) || "",
				Description: typeof args.data.Description == "string" ? args.data.Description.split(",") : [""],
				Period: args.data.TransactionDate
					? args.data.TransactionDate.split("-")[1] === "10" ||
					  args.data.TransactionDate.split("-")[1] === "11" ||
					  args.data.TransactionDate.split("-")[1] === "12"
						? args.data.TransactionDate.split("-")[1]
						: args.data.TransactionDate.split("-")[1].split("")[1]
					: "",
				PeriodYear: args.data.TransactionDate ? args.data.TransactionDate.split("-")[0] : "",
			}

			if (!newData.Analyis) {
				newData["Analyis"] = []
			}

			if (newData.SupplierName === "Add without supplier") {
				newData.SupplierName = ""
			}
			if (newData.SupplierName === "" && !newData.SupplierID && args.data["new-transaction-type"] === "2") {
				newTransactions.splice(0, 1)
				// Update the transaction data
				gridRef.current.dataSource = newTransactions

				// Refresh the grid to reflect the changes
				gridRef.current.refresh()
				// Check if supplier is valid
				Toast("error", t("supplier_empty_message"))
				return
			}

			// Check if amount is valid
			if (!newData.Amount && args.data["new-transaction-type"] === "2") {
				Toast("error", t("amount_empty_message"))
				return
			}

			// Calculate tco2e if transaction type is 2 and supplier CO2 intensity is not 0
			if (args.data["new-transaction-type"] === "2" && newData.supplierFactor != 0) {
				const tco2e = (newData.supplierFactor * newData.Amount) / 1000 / 1000
				if (newData.Scope === 1) {
					newData.Scope_1 = tco2e
				} else if (newData.Scope === 2) {
					newData.Scope_2 = tco2e
				} else if (newData.Scope === 3) {
					newData.Scope_3 = tco2e
				}
			}

			// If transaction type is 1, set transaction for edit details and show edit details dialog
			if (args.data["new-transaction-type"] === "1") {
				setTransactionsForEditDetails({ ...newData, isNewTransaction: true })
				setShowEditDetailsDialog(true)
				return
			}

			if (newData.SupplierName === "") {
				delete newData.SupplierID
				delete newData.SupplierName
				delete newData.supplierFactor
			}

			showLoader()

			const result = await app.addNewTransaction({ RegistrationNumber: RegistrationNumber, data: newData })

			if (result.success) {
				// Assign the transaction ID to the new data

				newData._id = result.transaction._id

				// Determine the scope type from the URL
				let scopeType = location.pathname.substring(location.pathname.lastIndexOf("/") + 1)
				let scopeTypeValue = scopeType === "scope1" ? 1 : scopeType === "scope2" ? 2 : scopeType === "scope3" ? 3 : 4

				if (scopeTypeValue == newData["Scope"] || !newData["Scope"] || scopeTypeValue == 4) {
					// Replace the existing transaction with the new data

					let newTransaction = { ...newData }

					newTransaction.Description =
						typeof args.data.Description == "string" ? args.data.Description.split(",") : [""]
					newTransaction.Scope_1 = newData.Scope_1
					newTransaction.Scope_2 = newData.Scope_2
					newTransaction.Scope_3 = newData.Scope_3
					// Calculate the total scope
					newTransaction.TotalScope = ((newData.Scope_1 + newData.Scope_2 + newData.Scope_3) * 1000).toFixed(2)

					// update top transaction because it automatically add transaction at the top
					// but few properties are missing so we need to replace top transaction with new transaction

					newTransactions.splice(0, 1, newTransaction)

					// Update the grid's dataSource with the new array
					gridRef.current.dataSource = newTransactions

					// Refresh the grid to reflect the changes
					gridRef.current.refresh()

					// Update the transaction data
					setTransactionData(newTransactions)

					setAllTransactions(newTransactions)
				} else if (newData["Scope"]) {
					// let suppose we are in scope 1 and we add transaction of scope 2
					// then we need to delete transaction from the grid because it automatically
					// adds one

					newTransactions.splice(0, 1)

					// Update the grid's dataSource with the new array
					gridRef.current.dataSource = newTransactions

					// Refresh the grid to reflect the changes
					gridRef.current.refresh()

					// Update the cache and the transaction data
					updateCache([newData["Scope"]])
				}

				// Hide the loader and refetch the suppliers data
				hideLoader()
				Toast("success", t("transaction_added_message"))
				suppliersData.refetch()
			} else {
				// If the result was not successful, handle the error
				//if (index > -1) {
				// If the transaction exists in the data, remove it
				newTransactions.splice(0, 1)
				// Update the transaction data
				setTransactionData(newTransactions)

				setAllTransactions(newTransactions)
				//}
				// Hide the loader and show an error message
				hideLoader()
				Toast("error", t("error_adding_transaction_message"))
			}
		}
	}

	// ----------------------------- Child grid for Lines configurations -----------------------------
	// We will show "lines" of transaction in nested grid. for that we need to configure a nested grid.
	// To show all the "lines", create a nested grid.
	const childGrid = {
		dataSource: lines,
		// 'TransactionID' properties connects transaction and lines data.
		queryString: "_id",
		allowPaging: true,
		pageSettings: { pageSize: 30, pageCount: 5 },
		editSettings: {
			allowEditing: true,
			mode: "Dialog",
			template: childGridDialogTemplate,
		},
		toolbar: ["Edit"],
		allowFiltering: true,
		filterSettings: { type: "Excel" },
		allowResizing: true,
		allowSorting: true,
		// On editing, adding,.. data in nested grid this function will be called. mostlty it is same as "actionComplete" function of "Main grid(Outer grid)".
		async actionBegin(args) {
			if (args.requestType === "beginEdit" || args.requestType === "add") {
				if (Browser.isDevice) {
					args.dialog.height = window.innerHeight - 90 + "px"
					args.dialog.dataBind()
				}
			}

			if (args.action === "edit" && args.requestType === "save") {
				// find the index pf row which was edited.
				let index = lines.findIndex((line) => line.RecordID === args.data.RecordID)
				if (index !== -1) {
					// revert data to pre-edit state. show edited data in grid only after successful updation in database,
					let newLines = lines
					newLines[index] = args.previousData
					setLines([...newLines])
					// create new transaction object with new data
					let newData = {}
					newData.netlifyID = netlifyID
					newData.RecordID = args.data.RecordID ? args.data.RecordID : ""
					newData.Description = args.data.Description ? args.data.Description : ""

					// call graphql mutation to update transaction data
					showLoader()

					const data = await app.updateLine()

					if (data.status === "done") {
						let newLines = lines
						newLines[index].Description = newData.Description
						setLines([...newLines])
					}

					hideLoader()
				}
			}
		},
		columns: [
			{
				field: "RecordID",
				headerText: t("RecordID"),
				width: 90,
				minWidth: 80,
				maxWidth: 300,
				isPrimaryKey: true,
			},
			{
				field: "AccountID",
				headerText: t("AccountID"),
				width: 90,
				minWidth: 80,
				maxWidth: 300,
			},
			{
				field: "AccountDescription",
				headerText: t("AccountDescription"),
				width: 90,
				minWidth: 80,
				maxWidth: 300,
			},
			{
				field: "Description",
				headerText: t("Description"),
				width: 200,
				minWidth: 80,
				maxWidth: 500,
			},
			{
				field: "CreditAmount.Amount",
				headerText: t("CreditAmount"),
				width: 100,
				minWidth: 80,
				maxWidth: 300,
			},
			{
				field: "DebitAmount.Amount",
				headerText: t("DebitAmount"),
				width: 100,
				minWidth: 80,
				maxWidth: 300,
			},
			{
				field: "ReferenceNumber",
				headerText: t("ReferenceNumber"),
				width: 140,
				minWidth: 80,
				maxWidth: 300,
			},
		],
	}

	const addEmployeeCommutingTransaction = () => {
		//showLoader()
		let data = {
			TransactionID: "TR-" + uuidv1(),
			AccountID: "7145",
			TransactionDate: `${reportingYear}-${new Date().getMonth() + 1}`,
			Scope_3_Category: 7,
			Description: ["Employee commuting"],
			AccountDescription: "Employee commuting",
			Amount: 0,
			SupplierName: "",
			Lines: [],
			Period: `${new Date().getMonth() + 1}`,
			PeriodYear: reportingYear,
			Scope: 3,
			isNewTransaction: true,
			isEmploeeCommutingTransaction: true,
		}
		setTransactionsForEditDetails(data)
		setShowEditDetailsDialog(true)
	}

	/**
	 * this function will calculate emission values on based of transaction's amount higher the amount heigher the emission.
	 * this will also set NaceCode as well
	 *
	 * @param {number} updatedScope1 - updated scope 1 value
	 * @param {number} updatedScope2 - updated scope 2 value
	 * @param {number} updatedScope3 - upsated scope 3 value
	 * @param {number} totalAmount - total amount of selected transactions
	 * @param {string} NaceCode - the NaceCode value
	 * @param {boolean} isNaceCode - we need to change NaceCode or not
	 * @param {boolean} isStatusChanged - we need to change status or not
	 * @param {number} Status - the status value
	 * @param {Array} selectedTransactions - the array of selected transactions
	 * @return {Array} the array of calculated emission values and NaceCode
	 */

	const calculateScopeValues = async (
		updatedScope1,
		updatedScope2,
		updatedScope3,
		totalAmount,
		NaceCode,
		isNaceCode,
		isStatusChanged,
		Status,
		selectedTransactions
	) => {
		let scope1 = updatedScope1 / 1000
		let scope2 = updatedScope2 / 1000
		let scope3 = updatedScope3 / 1000

		let DATA = []

		selectedTransactions.forEach((transaction) => {
			if (transaction.Amount == 0) {
				DATA.push({
					id: transaction._id.toString(),
					Scope_1: 0,
					Scope_2: 0,
					Scope_3: 0,
					Status: isStatusChanged ? Status : transaction.Status,
					NaceCode: isNaceCode ? NaceCode : transaction.NaceCode,
				})
			}
			if (transaction.Amount != "" && transaction.Amount != 0) {
				let percentage = transaction.Amount / totalAmount

				DATA.push({
					id: transaction._id.toString(),
					Scope_1: percentage * scope1,
					Scope_2: percentage * scope2,
					Scope_3: percentage * scope3,
					Status: isStatusChanged ? Status : transaction.Status,
					NaceCode: isNaceCode ? NaceCode : transaction.NaceCode,
				})
			}
		})
		return DATA
	}

	/**
	 * This function handles the update of multiple transactions
	 *
	 * @param {Object} data - The data containing the details
	 * of the transactions to be updated
	 *
	 */

	const handleUpdateMultipleTransactions = async (data) => {
		try {
			const { NaceCode, Notes, Status, Amount, isNaceCode, isStatusChanged } = data

			const newData = {
				Notes: Notes || "",
				RegistrationNumber,
				Scope_3_Category: data?.defaultScope3Catagory.value,
			}

			const selectedTransactions = gridRef.current.getSelectedRecords()

			const updatedData = await calculateScopeValues(
				data.scope1 || 0,
				data.scope2 || 0,
				data.scope3 || 0,
				Amount,
				NaceCode,
				isNaceCode,
				isStatusChanged,
				Status,
				selectedTransactions
			)

			newData["data"] = updatedData

			showLoader()

			// updateMultipleTransactions function will return status and modifieddata where
			// status value will be done if every thing goes right else will be error and
			// modified data will be the array of modified transactions

			const { status, modifieddata } = await app.updateMultipleTransactions(newData)

			if (status === "done") {
				updateTransactionInGrid(modifieddata)

				Toast("success", t("transactions_updated_message"))

				hideLoader()

				suppliersData.refetch()
			}
		} catch (error) {
			hideLoader()
			Toast("error", t("error_updating_multiple_transactions_message"))
			// Show error message to the user
		}
	}

	// ------------------------------ Handle Transactions Data ----------------------------
	useEffect(() => {
		// We need to make some changes in incoming transaction from database
		const changeData = (transactions) => {
			// Take all Lines from transaction data and store it in new variable.
			// because we need to provide all the lines seprately to child grid.
			let newTransactions = []
			let newLines = []
			let newEmissionSum = 0.0
			for (let i = 0; i < transactions.length; i++) {
				const { Lines } = transactions[i]
				if (Lines) {
					for (let j = 0; j < Lines.length; j++) {
						newLines.push({
							...Lines[j],
							lineNumber: j + 1,
							TransactionID: transactions[i].TransactionID,
							_id: transactions[i]._id,
						})
					}
				}
				newTransactions.push({ ...transactions[i] })
				if (!newTransactions[i].SupplierName) newTransactions[i].SupplierName = ""
				//newTransactions[i].id = transactions[i]._id.toString()
				newTransactions[i]._id = transactions[i]._id.toString()
				newTransactions[i].TotalScope =
					(transactions[i].Scope_1 + transactions[i].Scope_2 + transactions[i].Scope_3) * 1000
				newEmissionSum += newTransactions[i].TotalScope
				newTransactions[i].Amount = newTransactions[i].Amount || 0
				if (newTransactions[i].Period) {
					newTransactions[i].Period = parseInt(newTransactions[i].Period)
				}
				/* eslint eqeqeq: 0 */
				if (newTransactions[i].Scope_3_Category || newTransactions[i].Scope_3_Category == 0) {
					newTransactions[i].Scope_3_Category = parseInt(newTransactions[i].Scope_3_Category)
				}
				/* eslint eqeqeq: 0 */
				if ((newTransactions[i].Scope || newTransactions[i].Scope == 0) && newTransactions[i].Scope != "") {
					newTransactions[i].Scope = parseInt(newTransactions[i].Scope)
				}
			}
			setEmissionSum(newEmissionSum)
			setLines(newLines)
			return newTransactions
		}

		const getTransactionsDirectly = async () => {
			try {
				//  setTransactionLoading(true)

				if (transactions.data && transactions.data.length > 0) {
					if (transactions.data.length) {
						let transactionDisplayData = [...transactions.data].reverse()
						let changedTransactions = changeData(transactionDisplayData)
						setTransactionData(changedTransactions)
						setAllTransactions(changedTransactions)
						if (projects.length > 0 || departments.length > 0) {
							handleFilters([...projects, ...departments])
						}
						isTransactionFetched.current = true
					} else {
						setTransactionData(transactionData)
						setAllTransactions(transactionData)
						if (projects.length > 0 || departments.length > 0) {
							handleFilters([...projects, ...departments])
						}
					}
				} else {
					isTransactionFetched.current = true
					setTransactionData([])
				}
				// setTransactionLoading(false)
			} catch (error) {
				// handle error
			}
		}

		getTransactionsDirectly()
	}, [transactions.data])

	useEffect(() => {
		return () => {
			if (scopeType === "scope1") {
				updateCache([1])
			} else if (scopeType === "scope2") {
				updateCache([2])
			} else if (scopeType === "scope3") {
				updateCache([3])
			} else {
				updateCache([-1])
			}
		}
	}, [])

	useEffect(() => {
		if (!naceData?.data?.data) naceData.refetch()
	}, [RegistrationNumber])

	useEffect(() => {
		if (
			location.state &&
			location.state.transactionType == "EmployeeCommutingTransaction" &&
			isTransactionFetched.current
		) {
			addEmployeeCommutingTransaction()
			isAddEmployeeTransaction.current = true
			navigate(location.pathname)
			// 		history.replace(location.pathname, null)
			/* eslint react-hooks/exhaustive-deps: 0 */
		}
	}, [transactionData])

	const handleDeleteProjects = (projectId) => {
		try {
			// filter out the project from projects array which is not equal to projectId
			let newProjects = projects.filter((project) => project.value !== projectId)

			setProjects(newProjects)

			handleFilters([...newProjects, ...departments])
		} catch (error) {
			//
		}
	}

	const handleDeleteDepartments = (departmentId) => {
		try {
			// filter out the project from projects array which is not equal to projectId
			let newDepartments = departments.filter((department) => department.value !== departmentId)

			setDepartments(newDepartments)

			handleFilters([...projects, ...newDepartments])
		} catch (error) {
			//
		}
	}

	const Chip = ({ project, handleDelete, color }) => {
		return (
			<div key={project.value} className={`flex bg-${color} items-center w-fit rounded-full px-2 py-1`}>
				<p className=' text-white mr-1 text-xs m-0 p-0'>{project.label}</p>
				<FiX className='cursor-pointer text-white text-xs ' onClick={handleDelete} />
			</div>
		)
	}

	const toolbarTemplate = useCallback(
		() => (
			<div className='flex gap-3 px-6 items-center'>
				{toolbarOptions.map((op) => (
					<div
						aria-hidden
						key={op.text}
						onClick={() => copyData(op)}
						className='flex gap-1 p-2 items-center cursor-pointer hover:bg-slate-200'
					>
						{op.prefixIcon && <span className={`e-icons ${op.prefixIcon}`} />}
						<span>{op.text}</span>
					</div>
				))}

				{projects.length > 0 && (
					<>
						{projects.map((filter, index) => (
							<Chip
								key={filter.label}
								project={filter}
								index={index}
								color={"sky-600"}
								handleDelete={() => handleDeleteProjects(filter.value)}
							/>
						))}
					</>
				)}

				{departments.length > 0 && (
					<>
						{departments.map((filter, index) => (
							<Chip
								key={filter.label}
								project={filter}
								index={index}
								color={"amber-600"}
								handleDelete={() => handleDeleteDepartments(filter.value)}
							/>
						))}
					</>
				)}
			</div>
		),
		[projects, departments]
	)

	if (
		(transactions.isLoading ||
			suppliersData.isLoading ||
			naceData.isLoading ||
			transactions.isFetching ||
			suppliersList.isLoading ||
			!isTransactionFetched.current) &&
		isCompanyRegistered
	) {
		// If transactions data is stil not fetched then show loader on the screen.
		return (
			<div className='mainContainer'>
				<Spinner />
			</div>
		)
	} else {
		return (
			<>
				<Alert
					show={showAlert}
					text={t("confirm_delete_message")}
					title={t("deletion_warning")}
					handleClose={() => {
						setShowAlert(false)
					}}
					t={t}
					handleContinue={() => {
						deleteRecords()
					}}
				/>

				{openFiltersModal && (
					<Filters
						RegistrationNumber={RegistrationNumber}
						isOpen={openFiltersModal}
						handleFilters={handleFilters}
						setIsOpen={setOpenFiltersModal}
						projects={projects}
						setProjects={setProjects}
						setDepartments={setDepartments}
						departments={departments}
					/>
				)}

				<div id=''>
					<div id=''>
						<div className='control-pane'>
							<GridComponent
								// Transaction main grid configurations.
								id='transaction-grid'
								key={projects.length + departments.length}
								ref={gridRef}
								height='100%'
								dataSource={transactionData}
								childGrid={childGrid}
								toolbar={toolbarOptions}
								toolbarTemplate={toolbarTemplate}
								allowFiltering={true}
								filterSettings={filterSettings}
								allowPaging={true}
								pageSettings={pageSettings}
								editSettings={editSettings}
								selectionSettings={selectionsettings}
								contextMenuItems={contextMenuItems}
								contextMenuClick={contextMenuClick}
								allowSorting={true}
								allowResizing={true}
								allowGrouping={true}
								actionComplete={actionComplete}
								toolbarClick={copyData}
								allowExcelExport={true}
								locale={i18n.language}
							>
								<ColumnsDirective>
									{/* Set transaction grid columns */}
									<ColumnDirective type='checkbox' width='50'></ColumnDirective>

									<ColumnDirective field='_id' isPrimaryKey={true} visible={false}></ColumnDirective>

									<ColumnDirective
										field='TransactionID'
										headerText={t("TransactionID")}
										minWidth='80'
										width='120'
										maxWidth='300'
										textAlign='left'
									></ColumnDirective>

									<ColumnDirective
										field='Description.0'
										headerText={t("Description")}
										minWidth='80'
										width='160'
										maxWidth='1000'
										textAlign='left'
									></ColumnDirective>

									<ColumnDirective
										field='SupplierName'
										headerText={t("SupplierName")}
										minWidth='80'
										width='160'
										maxWidth='300'
										textAlign='left'
										isPrimaryKey={true}
									></ColumnDirective>

									<ColumnDirective
										field='Amount'
										headerText={t("DebitAmount")}
										minWidth='60'
										format='N0'
										width='120'
										maxWidth='300'
										textAlign='right'
									></ColumnDirective>

									<ColumnDirective
										field='TotalScope'
										headerText={t("Emissions-kgCo2e")}
										minWidth='80'
										//template={formateTotalEmission}
										format='N2'
										width='120'
										maxWidth='300'
										textAlign='right'
									></ColumnDirective>

									<ColumnDirective
										field='Status'
										template={imageTemplate}
										headerText={t("Status")}
										//filterTemplate={imageTemplate}
										filterItemTemplate={imageTemplate}
										minWidth='80'
										width='120'
										maxWidth='300'
										textAlign='center'
									></ColumnDirective>

									<ColumnDirective
										field='AccountID'
										headerText={t("AccountID")}
										minWidth='80'
										width='110'
										maxWidth='300'
										textAlign='center'
									></ColumnDirective>

									<ColumnDirective
										field='AccountDescription'
										headerText={t("AccountDescription")}
										minWidth='80'
										width='180'
										maxWidth='1000'
										textAlign='left'
									></ColumnDirective>

									<ColumnDirective
										field='Scope'
										headerText={t("Scope")}
										minWidth='80'
										width='80'
										maxWidth='1000'
										textAlign='center'
									></ColumnDirective>

									<ColumnDirective
										field='Scope_3_Category'
										headerText={t("Scope3Category")}
										minWidth='80'
										width='100'
										maxWidth='1000'
										textAlign='center'
									></ColumnDirective>

									<ColumnDirective
										field='Period'
										headerText={t("Period")}
										minWidth='60'
										width='60'
										maxWidth='300'
										textAlign='center'
									></ColumnDirective>

									<ColumnDirective
										field='PeriodYear'
										headerText={t("PeriodYear")}
										minWidth='50'
										width='120'
										maxWidth='300'
										textAlign='center'
									></ColumnDirective>
								</ColumnsDirective>

								{/* Footer configuratios. Show emission sum on the footer */}
								{/* <AggregatesDirective>
										<AggregateDirective>
											<AggregateColumnsDirective>
												<AggregateColumnDirective field="TotalScope" type="Sum" footerTemplate={footerEmissionSum} />
											</AggregateColumnsDirective>
										</AggregateDirective>
									</AggregatesDirective> */}
								<Inject
									services={[
										Page,
										Selection,
										ContextMenu,
										Toolbar,
										Edit,
										Filter,
										Group,
										Resize,
										DetailRow,
										Sort,
										Aggregate,
										InfiniteScroll,
										ExcelExport,
									]}
								/>
							</GridComponent>
						</div>
					</div>
					{!isCompanyRegistered && <AddReportingEntityModal props={props} />}
					{showMultiTransactionsEditDialog && (
						<UpdateMultipleTransactions
							showMultiTransactionsEditDialog={showMultiTransactionsEditDialog}
							setShowMultiTransactionsEditDialog={setShowMultiTransactionsEditDialog}
							data={multiData}
							reportingYear={reportingYear}
							//supplierData={suppliersData.data?.getSuppliers?.Suppliers}
							handleUpdateMultipleTransactions={handleUpdateMultipleTransactions}
							naceData={naceData?.data?.data || []}
							//setFlagEditValue={setFlagEditValue}
							t={t}
						/>
					)}
					{showEditAccountIDDialog && (
						<TransactionsEditAccountIdDialog
							showEditAccountIDDialog={showEditAccountIDDialog}
							setShowEditAccountIDDialog={setShowEditAccountIDDialog}
							updateAccountID={updateAccountID}
							gridRef={gridRef}
							hideLoader={hideLoader}
							showLoader={showLoader}
							transactionData={transactionData}
							location={location}
							//setAccountIDEditValue={setAccountIDEditValue}
							//setAccountIDEditDescription={setAccountIDEditDescription}
							t={t}
						/>
					)}
				</div>
				{showEditDetailsDialog ? (
					<>
						<div className='dull-background'></div>
						<TransactionDetailsDrawer
							isOpen={showEditDetailsDialog}
							setShowEditDetailsDialog={setShowEditDetailsDialog}
							transaction={transactionsForEditDetails}
							showLoader={showLoader}
							hideLoader={hideLoader}
							updateScopeValue={updateScopeValue}
							t={t}
						/>
					</>
				) : (
					<></>
				)}

				{loader}
			</>
		)
	}
}

export default Transactions
