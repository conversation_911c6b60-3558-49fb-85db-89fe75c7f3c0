/* eslint-disable import/no-unresolved */
import React, { useState } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"

import Button from "@/components/ui/Button" // Assuming alias setup
import Input from "@/components/ui/Input" // Assuming alias setup
import { useToast } from "@/hooks"

function Scope3Category2Content(props) {
	// Props expected:
	// - category2Rows: Array of existing emission objects [{ metric, factor, unit, scope1, scope2, scope3, isSaved }, ...]
	// - setCategory2Rows: Function to update the array of emission objects in the parent state
	// - updateCategory2Emission: Optional function to notify parent of changes (e.g., for backend updates)
	const { category2Rows = [], setCategory2Rows, updateCategory2Emission } = props
	const { t } = useTranslation("transactions")
	const toast = useToast()

	// Local state for the "Add New" form inputs
	const [metric, setMetric] = useState("") // Corresponds to 'Description'
	const [factor, setFactor] = useState(0.0)
	const [unit, setUnit] = useState(0.0)

	// Adds the new row data (from local state) to the main list (parent state)
	const addCategory2Row = (calculatedScope3) => {
		const newCategory2Row = {
			metric: metric,
			factor: Number(factor),
			unit: Number(unit),
			scope1: 0, // Assuming this category is Scope 3
			scope2: 0,
			scope3: Number(calculatedScope3), // Calculated emission
			Scope_3_Category: 2,
		}

		const newCategory2RowsList = [...category2Rows, newCategory2Row]
		setCategory2Rows(newCategory2RowsList)

		// Optionally notify parent about the addition
		if (updateCategory2Emission) {
			updateCategory2Emission(newCategory2Row, false) // false indicates addition
		}

		// Reset form fields
		setMetric("")
		setFactor(0.0)
		setUnit(0.0)
	}

	// Calculates emission and triggers adding the row
	const handleAddEmission = () => {
		// Basic validation
		if (metric.trim() === "") {
			toast("error", t("discription_error"))
			return
		}
		if (parseFloat(factor) <= 0 && parseFloat(unit) <= 0) {
			toast("error", t("factor_unit_greater_0_error"))
			return
		}
		const numFactor = parseFloat(factor)
		const numUnit = parseFloat(unit)

		// Calculation based on calculateCategory2Emissions: factor * unit
		let calculatedScope3 = numFactor * numUnit
		addCategory2Row(calculatedScope3)
	}

	// Deletes a row from the list by index
	const deleteCategory2Row = (index) => {
		const rowToDelete = category2Rows[index]
		const newCategory2RowsList = [...category2Rows]
		newCategory2RowsList.splice(index, 1)

		setCategory2Rows(newCategory2RowsList)

		// Optionally notify parent about the deletion
		if (updateCategory2Emission) {
			updateCategory2Emission(rowToDelete, true) // true indicates deletion
		}
	}

	const handleFocus = (event) => event.target.select()

	return (
		<>
			{/* Display existing/saved rows */}
			{category2Rows.map((category2Item, index) => (
				// Using the display structure from the original component's 'isSaved' block
				// Assuming category2Item has an 'isSaved' property or similar logic applies
				<div key={index}>
					<div className='grid grid-cols-5 gap-10 my-1 saved-emission'>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Metric")}</label>
							<div>{category2Item.metric}</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Factor")}</label>
							<div className='flex relative'>
								<span>{category2Item.factor}</span>
								<span className='custom-span-unit-value-save'>{t("Co2e")}</span> {/* Assuming unit */}
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Units")}</label>
							<div className='flex relative'>
								<span>{category2Item.unit}</span>
								<span className='custom-span-unit-value-save'>{t("Pcs")}</span> {/* Assuming unit */}
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("TotalCo2e")}</label>
							<div className='flex relative'>
								<span>
									{Number(
										(category2Item.scope1 || 0) + (category2Item.scope2 || 0) + (category2Item.scope3 || 0)
									).toFixed(2)}
								</span>
								<span className='text-nowrap custom-span-unit-value-save'>{t("kg")}</span>
							</div>
						</div>

						<div className='!items-center delete-icon'>
							<span aria-hidden onClick={() => deleteCategory2Row(index)} style={{ cursor: "pointer" }}>
								<RiDeleteBin6Line />
							</span>
						</div>
					</div>
					<hr />
				</div>
			))}

			{/* Form for adding new rows */}
			<div className='grid grid-cols-4 gap-10 my-2'>
				<div>
					<Input
						label={t("Description")} // Label from original component's input
						type='text'
						labelColor='text-sky-500'
						value={metric}
						handleChange={(e) => setMetric(e.target.value)}
						handleFocus={handleFocus}
						placeholder={t("Enter description")} // Added placeholder
					/>
				</div>
				<div>
					<Input
						label={t("Factor")}
						labelColor='text-sky-500'
						type='number'
						value={factor}
						handleChange={(e) => setFactor(e.target.value)}
						handleFocus={handleFocus}
						unit={t("Co2e")} // Unit from original component's input
						min='0' // Ensure non-negative
						step='any' // Allow decimals
					/>
				</div>
				<div>
					<Input
						label={t("Units")}
						labelColor='text-sky-500'
						type='number'
						value={unit}
						handleChange={(e) => setUnit(e.target.value)}
						handleFocus={handleFocus}
						unit={t("Pcs")} // Unit from original component's input
						min='0' // Ensure non-negative
						step='any' // Allow decimals
					/>
				</div>
				<div className='self-end mb-1'>
					<Button title={`${t("Add")} | +`} handleClick={handleAddEmission} color='sky-500' />
				</div>
			</div>
		</>
	)
}

export default Scope3Category2Content
