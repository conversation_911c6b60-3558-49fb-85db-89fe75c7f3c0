/* eslint-disable import/no-unresolved */
import React, { useEffect, useMemo, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"
import Select from "react-select"

import Button from "../../../../../components/ui/Button"
import Input from "../../../../../components/ui/Input"
import Factors from "../contants/factors.beis.json"
import WTTFACTORS from "../contants/WTTfactors.beis.json"

import { useToast } from "@/hooks"

//import FactorsData from "./factors.beis.json"

function Category6LT(props) {
	const { landTransportRows, setLandTransportRows, updateCategory6LT } = props
	const peopleInputRef = useRef()

	const [travelModesOptions, setTravelModesOptions] = useState([])

	const [travelMode, setTravelMode] = useState("")

	const [sizes, setSizes] = useState([])

	const [driveTrains, setDriveTrains] = useState([])

	const [driveTrain, setDriveTrain] = useState("")

	const [factor, setFactor] = useState(0.0)

	const [size, setSize] = useState("")

	const [id, setId] = useState("")

	const [distance, setDistance] = useState("")

	const [people, setPeople] = useState("")

	const toast = useToast()

	const { t } = useTranslation("transactions")

	const getUniqueSize = (travelMode) => {
		try {
			const uniqueLabelsMap = new Map()

			Factors.forEach((factor) => {
				if (factor["Level 2"] === travelMode) {
					const size = {
						label: factor["Level 3"],
						value: factor["ID"],
						factor: factor["GHG Conversion Factor"],
					}
					// Only keep one entry per unique label
					uniqueLabelsMap.set(size.label, size)
				}
			})

			// Convert map values back to array
			const uniqueSizes = Array.from(uniqueLabelsMap.values())

			setSizes(uniqueSizes)
		} catch (error) {
			console.error("Error processing Factors:", error)
		}
	}

	const getUniqueDriveTrains = (size) => {
		try {
			const uniqueLabelsMap = new Map()

			Factors.forEach((factor) => {
				if (factor["Level 3"] === size && factor["Level 2"] === travelMode.label) {
					// Check if Column Text is not NaN (either as string or number)
					if (
						factor["Column Text"] &&
						!(typeof factor["Column Text"] === "object" && factor["Column Text"].$numberDouble === "NaN")
					) {
						const driveTrain = {
							label: factor["Column Text"],
							value: factor["ID"],
							factor: factor["GHG Conversion Factor"],
						}
						// Only keep one entry per unique label
						uniqueLabelsMap.set(driveTrain.label, driveTrain)
					}
				}
			})

			// Convert map values back to array
			const uniqueDriveTrains = Array.from(uniqueLabelsMap.values())

			setDriveTrains(uniqueDriveTrains.length === 0 ? [] : uniqueDriveTrains)
		} catch (error) {
			// handle error
		}
	}

	const travelModeChange = (event) => {
		// const newCategory6LT = { ...category6LT }

		// this function will get me unique size based on the selected travel mode
		getUniqueSize(event.label)

		// here we are seting travel mode in state so that we can use it later
		setTravelMode(event)

		setTravelMode(event)

		setSize("")

		setFactor(event.factor)

		setId(event.value)

		// newCategory6LT.travelMode = event
		// newCategory6LT.size = ""
		// newCategory6LT.factor = event.factor
		// newCategory6LT.ID = event.value
		//props.updateCategory6LT(index, newCategory6LT)
		peopleInputRef.current.focus()
	}

	const handleSizeChange = (event) => {
		// const newCategory6LT = { ...category6LT }

		// this function will get me unique drive train based on the selected travel mode and size

		getUniqueDriveTrains(event.label)

		// here we are seting size in state so that we can use it later

		setSize(event)

		setFactor(event.factor)

		setDriveTrain("")

		setId(event.value)

		//newCategory6LT.size = event
		//newCategory6LT.factor = event.factor
		//newCategory6LT.driveTrain = ""

		// newCategory6LT.ID = event.value
		//props.updateCategory6LT(index, newCategory6LT)
		peopleInputRef.current.focus()
	}

	const driveTrainChange = (event) => {
		//const newCategory6LT = { ...category6LT }

		// here we are seting drive train in state so that we can use it later

		setDriveTrain(event)
		setFactor(event.factor)
		setId(event.value)

		// newCategory6LT.fuelType = event
		//newCategory6LT.driveTrain = event
		//newCategory6LT.factor = event.factor
		//newCategory6LT.ID = event.value
		//props.updateCategory6LT(index, newCategory6LT)
		peopleInputRef.current.focus()
	}

	const distanceChange = (event) => {
		//const newDistance = event.target.value
		//const newCategory6LT = { ...category6LT }
		//newCategory6LT.distance = newDistance
		//props.updateCategory6LT(index, newCategory6LT)

		setDistance(event.target.value)
	}

	const peopleChange = (event) => {
		// const people = event.target.value
		// const newCategory6LT = { ...category6LT }
		// newCategory6LT.people = people
		// props.updateCategory6LT(index, newCategory6LT)
		setPeople(event.target.value)
	}

	const getWTTFactor = (targetID) => {
		// Extract the relevant part of the target ID (3045_4_1 from 27_905_3045_4_1)
		const targetPattern = targetID.split("_").slice(-3).join("_")

		// Iterate through the dataset to find a matching ID pattern
		for (const item of WTTFACTORS) {
			const itemPattern = item.ID.split("_").slice(-3).join("_")

			// Check if extracted parts match
			if (itemPattern === targetPattern) {
				return item["GHG Conversion Factor"]
			}
		}

		// Return null if no match is found
		return null
	}

	const addCategory6LTRow = (scope3) => {
		const newCategory6LT = {
			travelMode: travelMode,
			size: size,
			driveTrain: driveTrain,
			people: people,
			Scope_3_Category: 6,
			distance: distance,
			scope3: scope3,
		}

		const newRows = [...landTransportRows, newCategory6LT]

		setLandTransportRows(newRows)

		updateCategory6LT(newCategory6LT, false)

		//props.addCategory6LT(index)
	}

	const calculateCategory6LTEmission = async () => {
		if (travelMode === "" || size === "" || people === "" || distance === "") {
			toast("error", t("select_all_fields"))
			return
		}

		if (driveTrains.length > 0 && driveTrain === "") {
			toast("error", t("select_drive_train"))
			return
		}

		let WTTFactor = getWTTFactor(id)

		let scope3 = (WTTFactor + factor) * people * distance

		addCategory6LTRow(scope3)
	}

	const deleteCategory6LTField = (index) => {
		const newRows = [...landTransportRows]

		newRows.splice(index, 1)

		setLandTransportRows(newRows)

		updateCategory6LT(landTransportRows[index], true)

		// props.deleteCategory6LT(index)
	}

	const uniqueTravelModes = useMemo(() => {
		if (!Array.isArray(Factors)) return []

		const uniqueLabelsMap = new Map()
		try {
			Factors.forEach(({ "Level 2": label, ID: value, "GHG Conversion Factor": factor }) => {
				uniqueLabelsMap.set(label, { label, value, factor })
			})
		} catch (error) {
			console.error("Error processing Factors:", error)
			return []
		}
		return Array.from(uniqueLabelsMap.values())
	}, [])

	// Set state only when uniqueTravelModes changes
	useEffect(() => {
		setTravelModesOptions(uniqueTravelModes)
	}, [uniqueTravelModes])

	const handleFocus = (event) => event.target.select()

	return (
		<>
			{landTransportRows.map((category6LT, index) => (
				<div key={index}>
					<div className='grid grid-cols-7 gap-4 saved-emission'>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("TravelMode")}</label>
							<div>{category6LT.travelMode.label}</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Size")}</label>
							<div>{category6LT.size.label}</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Drivetrain")}</label>
							<div>{category6LT.driveTrain.label}</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("People")}</label>
							<div className='d-flex position-relative'>
								<span>{category6LT.people}</span>
								<span className='text-nowrap custom-span-unit-value-save'> people</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Distance")}</label>
							<div className='d-flex position-relative'>
								<span>{category6LT.distance}</span>
								<span className='text-nowrap custom-span-unit-value-save'> Km</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("TotalCo2e")}</label>
							<div className='d-flex position-relative'>
								<span>{Number(category6LT.scope3).toFixed(2)}</span>
								<span className='text-nowrap custom-span-unit-value-save'>{t("kg")}</span>
							</div>
						</div>

						<div className='!items-center delete-icon'>
							<span aria-hidden onClick={() => deleteCategory6LTField(index)}>
								<RiDeleteBin6Line />
							</span>
						</div>
					</div>
					<hr />
				</div>
			))}
			<div className='grid grid-cols-3 gap-2 my-3'>
				<div>
					<label className='text-slate-800 font-semibold whitespace-nowrap' htmlFor={"c6lt-transport-type"}>
						{t("TravelMode")}
					</label>
					<Select
						id={"c6lt-transport-type"}
						value={travelMode}
						onChange={travelModeChange}
						options={travelModesOptions}
					/>
				</div>
				<div>
					<label className='text-slate-800 font-semibold whitespace-nowrap' htmlFor={"c6lt-fuel-type-"}>
						{t("Size")}
					</label>
					<Select id={"c6lt-fuel-type-"} value={size} onChange={handleSizeChange} options={sizes} />
				</div>
				<div>
					<label className='text-slate-800 font-semibold whitespace-nowrap' htmlFor={"c6lt-fuel-type-"}>
						{t("Drivetrain")}
					</label>
					<Select
						id={"c6lt-fuel-type-"}
						value={driveTrain}
						isDisabled={driveTrains.length === 0}
						onChange={driveTrainChange}
						options={driveTrains}
					/>
				</div>

				<div>
					<Input
						label={t("People")}
						ref={peopleInputRef}
						value={people}
						handleChange={peopleChange}
						handleFocus={handleFocus}
						placeholder='people'
						labelColor='text-sky-500'
						type='number'
						unit={""}
					/>
				</div>
				<div>
					<Input
						label={t("Distance")}
						value={distance}
						handleChange={distanceChange}
						handleFocus={handleFocus}
						placeholder='distance'
						labelColor='text-sky-500'
						type='number'
						unit={""}
					/>
				</div>
				<div className='self-end mb-1'>
					<Button title={`${t("Add")} | +`} handleClick={() => calculateCategory6LTEmission()} color='sky-500' />
				</div>
			</div>
		</>
	)
}

export default Category6LT
