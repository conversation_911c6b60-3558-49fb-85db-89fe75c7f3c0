import { UserProfile, useUser } from "@clerk/clerk-react"
import React from "react"
//import { RiHome2Line } from "react-icons/ri"
//import { Link } from "react-router-dom"
import "../../styles/usersProfile.css"

function UsersProfile() {
	// const addOrganizationLink = () => {
	// 	setTimeout(() => {
	// 		let userprofile = document.getElementsByClassName("cl-userProfile-root cl-rootBox")
	// 		if (userprofile) {
	// 			let navbar = document.getElementsByClassName("cl-navbarButtons")
	// 			if (navbar.length > 0) {
	// 				let org = document.getElementById("org-link")
	// 				navbar[0].appendChild(org)
	// 			}
	// 		}
	// 	}, 2000)
	// }

	// useEffect(() => {
	// 	addOrganizationLink()
	// }, [])

	const user = useUser()

	console.log(user)

	return <UserProfile></UserProfile>

	/* <Link to="/organization" className="cl-navbar-link-custom" id="org-link">
					<span>
						<RiHome2Line className="mr-1 cl-icon-custom" /> Company
					</span>
				</Link> */
}

export default UsersProfile
