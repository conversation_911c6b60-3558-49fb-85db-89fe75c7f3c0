import React, { useState, useRef } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"
import Select from "react-select"

import Button from "../../../../../components/ui/Button" // Adjusted path
import Input from "../../../../../components/ui/Input" // Adjusted path
import { useToast } from "../../../../../hooks"
import { co2_mix } from "../../scope2/constants/electricTransportation"
import { BUILDINGS_FACTORS } from "../contants/category8.js" // Adjusted path, assuming co2_mix is here

// Component to manage Category 8 Leased Assets (Upstream)
export default function Scope3Category8Content(props) {
	const {
		category8Rows = [], // Array of category 8 objects from parent state
		setCategory8Rows, // Function to update the parent state array
		updateCategory8Emission, // Function to notify parent of changes for total calculation
		PeriodYear, // Year needed for emission factors { label: "YYYY", value: YYYY }
	} = props

	const { t } = useTranslation("transactions")
	const sizeInputRef = useRef() // Ref for size input in the add form

	const toast = useToast()

	// --- Constants for Select options ---
	const BUILDINGS_TYPES = [
		{ label: t("Barnehage"), value: "Barnehage" },
		{ label: t("Kontor"), value: "Kontor" },
		{ label: t("Skole"), value: "Skole" },
		{ label: t("Universitet"), value: "Universitet" },
		{ label: t("Sykehus"), value: "Sykehus" },
		{ label: t("Sykehjem"), value: "Sykehjem" },
		{ label: t("Hotell"), value: "Hotell" },
		{ label: t("Idrettsbygg"), value: "Idrettsbygg" },
		{ label: t("Forretning"), value: "Forretning" },
		{ label: t("Kulturbygning"), value: "Kulturbygning" },
		{ label: t("Lett industri"), value: "Lett industri" },
	]

	const PERIOD_TYPES = [
		{ label: t("Yearly"), value: "kwh_year" },
		{ label: t("Monthly"), value: "kwh_month" },
		{ label: t("Quarterly"), value: "kwh_quarter" },
	]

	// --- State for the "Add New" form ---
	const [buildingType, setBuildingType] = useState(BUILDINGS_TYPES[0])
	const [periodType, setPeriodType] = useState(PERIOD_TYPES[0]) // e.g., { label: "Yearly", value: "kwh_year" }
	const [factor, setFactor] = useState(0) // kWh/m2 based on building and period type
	const [size, setSize] = useState(0)

	// --- Helper function to get factor ---
	const getFactor = (buildingTypeValue, periodTypeValue) => {
		if (!buildingTypeValue || !periodTypeValue) return 0
		const buildingData = BUILDINGS_FACTORS.find((f) => f.Category === buildingTypeValue)
		return buildingData ? buildingData[periodTypeValue] || 0 : 0
	}

	// --- Input Handlers for "Add New" form ---
	const handleBuildingTypeChange = (selectedOption) => {
		setBuildingType(selectedOption)
		const newFactor = getFactor(selectedOption?.value, periodType?.value)
		setFactor(newFactor)
		sizeInputRef.current?.focus()
	}

	const handlePeriodTypeChange = (selectedOption) => {
		setPeriodType(selectedOption)
		const newFactor = getFactor(buildingType?.value, selectedOption?.value)
		setFactor(newFactor)
	}

	// Allow manual override of factor if needed
	const handleFactorChange = (event) => {
		setFactor(event.target.value)
	}

	const handleSizeChange = (event) => {
		setSize(event.target.value)
	}

	const handleFocus = (event) => event.target.select()

	// --- Add/Delete Logic ---

	// Adds the validated and calculated new row to the state
	const addCategory8Row = (calculatedEmission) => {
		const newRow = {
			// id: generate unique id if needed
			buildingType: buildingType,
			type: periodType, // Stores { label: "Yearly", value: "kwh_year" }
			factor: Number(factor),
			size: Number(size),
			scope1: 0, // Assuming 0 for scope 1 & 2 for this category
			scope2: 0,
			scope3: Number(calculatedEmission.scope3),
			// Include other calculated values if the parent needs them directly
			kwh: calculatedEmission.kwh,
			nuclear: calculatedEmission.nuclear,
			non_renewable: calculatedEmission.non_renewable,
			renewable: calculatedEmission.renewable,
		}

		const updatedRows = [...category8Rows, newRow]
		setCategory8Rows(updatedRows) // Update parent state

		// Notify parent for recalculation
		if (updateCategory8Emission) {
			updateCategory8Emission(newRow, false) // false indicates addition
		}

		// Reset form (consider resetting factor based on new defaults)
		setBuildingType(BUILDINGS_TYPES[0])
		setPeriodType(PERIOD_TYPES[0])
		setFactor(getFactor(BUILDINGS_TYPES[0]?.value, PERIOD_TYPES[0]?.value))
		setSize(0)
	}

	// Validates form, calculates emission for the single new row, and calls addCategory8Row
	const calculateCategory8Emissions = () => {
		let nuclear = 0

		let non_renewable = 0

		const scope3Factor = co2_mix[PeriodYear]["NO"]?.co2 || 0
		const nuclearFactor = co2_mix[PeriodYear]["NO"]?.nuclear || 0
		const non_renewable_energyFactor = co2_mix[PeriodYear]["NO"]?.fossil || 0
		const renewableFactor = co2_mix[PeriodYear]["NO"]?.renewables || 0

		let scope3 = 0

		let renewable = 0

		if (parseFloat(factor) <= 0 && parseFloat(size) <= 0) {
			toast("error", t("category_8_error"))
			return
		}

		let kwh = parseFloat(factor) * parseFloat(size)

		scope3 = scope3Factor * kwh

		nuclear = nuclearFactor * kwh
		renewable = renewableFactor * kwh

		non_renewable += non_renewable_energyFactor * kwh

		addCategory8Row({
			scope3,
			kwh,
			nuclear,
			non_renewable,
			renewable,
		})
	}

	// Deletes a row from the list by index
	const deleteCategory8Row = (index) => {
		const rowToDelete = category8Rows[index]
		const updatedRows = category8Rows.filter((_, i) => i !== index)
		setCategory8Rows(updatedRows) // Update parent state

		// Notify parent for recalculation
		if (updateCategory8Emission) {
			updateCategory8Emission(rowToDelete, true) // true indicates deletion
		}
	}

	// --- Effect to set initial factor ---
	React.useEffect(() => {
		setFactor(getFactor(buildingType?.value, periodType?.value))
	}, [buildingType, periodType]) // Recalculate factor when types change

	return (
		<div className='scope-category-container'>
			<div className='bg-amber-100 border-l-4 mb-3 border-amber-500 text-black p-2 text-sm' role='alert'>
				{t("Category8Info")} {/* Assuming this translation key exists */}
			</div>

			{category8Rows.map((row, index) => (
				<div key={row.id || index}>
					{" "}
					{/* Use a stable key if available */}
					<div className='grid grid-cols-6 gap-4 my-1 saved-emission items-center text-sm'>
						{" "}
						{/* Adjusted columns */}
						<div>
							<label className='text-slate-600 text-xs font-medium whitespace-nowrap'>{t("BuildingType")}</label>
							<div>{row.buildingType?.label || "N/A"}</div>
						</div>
						<div>
							<label className='text-slate-600 text-xs font-medium whitespace-nowrap'>{t("Type")}</label>
							<div>{row.type?.label || "N/A"}</div> {/* Display label of period type */}
						</div>
						<div>
							<label className='text-slate-600 text-xs font-medium whitespace-nowrap'>{t("Factor")}</label>
							<div className='flex relative'>
								<span>{Number(row.factor || 0).toFixed(2)}</span>
								<span className='text-xs text-gray-500 ml-1 mt-0.5'>{"kWh/m2"}</span>
							</div>
						</div>
						<div>
							<label className='text-slate-600 text-xs font-medium whitespace-nowrap'>{t("Size")}</label>
							<div className='flex relative'>
								<span>{Number(row.size || 0).toFixed(2)}</span>
								<span className='text-xs text-gray-500 ml-1 mt-0.5'>{"m2"}</span>
							</div>
						</div>
						<div>
							<label className='text-slate-600 text-xs font-medium whitespace-nowrap'>{t("TotalCo2e")}</label>
							<div className='flex relative'>
								<span>{Number(row.scope3 || 0).toFixed(2)}</span>
								<span className='text-xs text-gray-500 ml-1 mt-0.5'>{t("kg")}</span>
							</div>
						</div>
						<div className='flex justify-end items-center'>
							<button
								type='button'
								onClick={() => deleteCategory8Row(index)}
								className='text-red-500 hover:text-red-700 p-1'
								aria-label={t("DeleteAssetRow")}
							>
								<RiDeleteBin6Line size={24} />
							</button>
						</div>
					</div>
					<hr className='my-2' />
				</div>
			))}

			{/* Form for adding new rows */}
			<div className='grid grid-cols-5 gap-4 my-3 pt-3 border-t border-gray-200'>
				{" "}
				{/* Adjusted columns */}
				<div>
					<label className='text-slate-800 text-sm font-semibold whitespace-nowrap' htmlFor='c8-new-building-type'>
						{t("BuildingType")}
					</label>
					<Select
						id='c8-new-building-type'
						value={buildingType}
						onChange={handleBuildingTypeChange}
						options={BUILDINGS_TYPES}
						placeholder={t("Select...")}
						classNamePrefix='react-select'
					/>
				</div>
				<div>
					<label className='text-slate-800 text-sm font-semibold whitespace-nowrap' htmlFor='c8-new-period-type'>
						{t("Type")}
					</label>
					<Select
						id='c8-new-period-type'
						value={periodType}
						onChange={handlePeriodTypeChange}
						options={PERIOD_TYPES}
						classNamePrefix='react-select'
					/>
				</div>
				<div>
					<Input
						label={t("Factor")}
						id='c8-new-factor'
						placeholder='0.00'
						type='number'
						min='0'
						step='any'
						unit={"kWh/m2"}
						value={factor}
						handleChange={handleFactorChange}
						handleFocus={handleFocus}
						labelClassName='text-sm font-semibold'
					/>
				</div>
				<div>
					<Input
						label={t("Size")}
						id='c8-new-size'
						ref={sizeInputRef}
						placeholder='0.00'
						type='number'
						min='0'
						step='any'
						unit={"m2"}
						value={size}
						handleChange={handleSizeChange}
						handleFocus={handleFocus}
						labelClassName='text-sm font-semibold'
					/>
				</div>
				<div className='self-end mb-1'>
					<Button
						title={`${t("Add")} | +`}
						handleClick={() => calculateCategory8Emissions()}
						color='sky-500'
						size='sm'
					/>
				</div>
			</div>
		</div>
	)
}
