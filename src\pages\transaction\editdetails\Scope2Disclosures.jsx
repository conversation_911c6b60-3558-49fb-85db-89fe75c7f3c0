import React, { useState } from "react"
import { RiInformationLine } from "react-icons/ri"

import DisclosureComponent from "./DisclosureComponent"
import ElectricityContent from "./scope2/ElectricityContent"
import ElectricTransportationContent from "./scope2/ElectricTransportationContent"
import HeatContent from "./scope2/HeatContent"
import OtherScope2Content from "./scope2/OtherScope2Content" // Import the component

// const OtherScope2Content = () => <div>Content for Other scope 2 emissions...</div> // Remove placeholder

// Example of adding an icon to the title as shown in the image
const TitleWithIcon = ({ t }) => (
	<span className='flex items-center gap-1 text-sm/6 font-medium text-black group-data-[open]:text-sky-600'>
		{t("EmissionsFromElectricity")}
		<span className='tooltip'>
			<RiInformationLine className=' text-red-500 tooltip-icon' />
			<span className='tooltiptext2'>{t("EmissionsFromElectricitytooltip")}</span>
		</span>
	</span>
)

const Scope2Disclosures = ({ t, data, updateData, setScope }) => {
	const [electricityRows, setElectricityRows] = useState([])

	const [heatRows, setHeatRows] = useState([])

	const [electricTransportRows, setElectricTransportRows] = useState([])

	const [otherScope2Rows, setOtherScope2Rows] = useState([]) // Add state for other scope 2 rows
	const updateElectricityEmission = async (newRow, isDeleted = false, updatedElectricityRows) => {
		let {
			kwh = 0,
			Scope_2 = 0,
			Scope_3 = 0,
			nuclear = 0,
			locationBased = 0,
			marketBased = 0,
			consumptionBased = 0,
			renewable_energy = 0,
			non_renewable_energy = 0,
		} = data

		// Convert to numbers
		kwh = Number(kwh)
		Scope_2 = Number(Scope_2)
		Scope_3 = Number(Scope_3)
		nuclear = Number(nuclear)
		locationBased = Number(locationBased)
		marketBased = Number(marketBased)
		consumptionBased = Number(consumptionBased)
		renewable_energy = Number(renewable_energy)
		non_renewable_energy = Number(non_renewable_energy)

		if (isDeleted) {
			kwh -= newRow.kwh
			Scope_2 -= newRow.scope2
			Scope_3 -= newRow.scope3
			nuclear -= newRow.nuclear
			locationBased -= newRow.locationBased
			marketBased -= newRow.marketBased
			consumptionBased -= newRow.consumptionBased
			renewable_energy -= newRow.renewable_energy
			non_renewable_energy -= newRow.non_renewable_energy
		} else {
			kwh += newRow.kwh
			Scope_2 += newRow.scope2
			Scope_3 += newRow.scope3
			nuclear += newRow.nuclear
			locationBased += newRow.locationBased
			marketBased += newRow.marketBased
			consumptionBased += newRow.consumptionBased
			renewable_energy += newRow.renewable_energy
			non_renewable_energy += newRow.non_renewable_energy
		}

		setScope(2)
		await updateData({
			kwh: parseFloat(Number(kwh).toFixed(2)),
			Scope_2: parseFloat(Number(Scope_2).toFixed(2)),
			Scope_3: parseFloat(Number(Scope_3).toFixed(2)),
			nuclear: parseFloat(Number(nuclear).toFixed(2)),
			locationBased: parseFloat(Number(locationBased).toFixed(2)),
			marketBased: parseFloat(Number(marketBased).toFixed(2)),
			consumptionBased: parseFloat(Number(consumptionBased).toFixed(2)),
			renewable_energy: parseFloat(Number(renewable_energy).toFixed(2)),
			non_renewable_energy: parseFloat(Number(non_renewable_energy).toFixed(2)),
			scope2ElectricityRows: updatedElectricityRows,
		})
	}

	const updateHeatEmission = async (newRow, isDeleted = false, updatedHeatRows) => {
		let { Scope_2 = 0, Scope_3 = 0 } = data

		// Convert to numbers
		Scope_2 = Number(Scope_2)
		Scope_3 = Number(Scope_3)

		setScope(2)

		if (isDeleted) {
			Scope_2 -= newRow.scope2
			Scope_3 -= newRow.scope3
		} else {
			Scope_2 += newRow.scope2
			Scope_3 += newRow.scope3
		}

		await updateData({
			Scope_2: parseFloat(Number(Scope_2).toFixed(2)),
			Scope_3: parseFloat(Number(Scope_3).toFixed(2)),
			scope2HeatRows: updatedHeatRows,
		})
	}

	const updateElectricTransportEmission = async (newRow, isDeleted = false, updatedElectricTransportRows) => {
		let {
			kwh = 0,
			Scope_2 = 0,
			Scope_3 = 0,
			locationBased = 0,
			marketBased = 0,
			consumptionBased = 0,
			renewable_energy = 0,
			non_renewable_energy = 0,
		} = data

		// Convert to numbers
		kwh = Number(kwh)
		Scope_2 = Number(Scope_2)
		Scope_3 = Number(Scope_3)
		locationBased = Number(locationBased)
		marketBased = Number(marketBased)
		consumptionBased = Number(consumptionBased)
		renewable_energy = Number(renewable_energy)
		non_renewable_energy = Number(non_renewable_energy)

		if (isDeleted) {
			kwh -= newRow.kwh
			Scope_2 -= newRow.scope2
			Scope_3 -= newRow.scope3
			locationBased -= newRow.locationBased
			marketBased -= newRow.marketBased
			consumptionBased -= newRow.consumptionBased
			renewable_energy -= newRow.renewable_energy
			non_renewable_energy -= newRow.non_renewable_energy
		} else {
			kwh += newRow.kwh
			Scope_2 += newRow.scope2
			Scope_3 += newRow.scope3
			locationBased += newRow.locationBased
			marketBased += newRow.marketBased
			consumptionBased += newRow.consumptionBased
			renewable_energy += newRow.renewable_energy
			non_renewable_energy += newRow.non_renewable_energy
		}

		setScope(2)
		await updateData({
			kwh: parseFloat(Number(kwh).toFixed(2)),
			Scope_2: parseFloat(Number(Scope_2).toFixed(2)),
			Scope_3: parseFloat(Number(Scope_3).toFixed(2)),
			locationBased: parseFloat(Number(locationBased).toFixed(2)),
			marketBased: parseFloat(Number(marketBased).toFixed(2)),
			consumptionBased: parseFloat(Number(consumptionBased).toFixed(2)),
			renewable_energy: parseFloat(Number(renewable_energy).toFixed(2)),
			non_renewable_energy: parseFloat(Number(non_renewable_energy).toFixed(2)),
			scope2ElectricTransportRows: updatedElectricTransportRows,
		})
	}

	const updateOtherScope2Emission = async (newRow, isDeleted = false, scope2OtherEmission) => {
		let { Scope_2 = 0 } = data

		// Convert to number
		Scope_2 = Number(Scope_2)

		if (isDeleted) {
			Scope_2 -= newRow.scope2 || 0
		} else {
			Scope_2 += newRow.scope2 || 0
		}

		setScope(2)
		await updateData({
			Scope_2: parseFloat(Number(Scope_2).toFixed(2)),
			scope2OtherEmissionRows: scope2OtherEmission,
		})
	}

	return (
		<div className='[&>:not(:first-child)]:border-t-2'>
			{/* Example of using the TitleWithIcon component */}
			<DisclosureComponent title={<TitleWithIcon t={t} text='Emissions from electricity' />}>
				<ElectricityContent
					updateElectricityEmission={updateElectricityEmission}
					electricityRows={electricityRows}
					setElectricityRows={setElectricityRows}
					period={data.period}
					PeriodYear={data.PeriodYear}
				/>
			</DisclosureComponent>
			<DisclosureComponent title={t("EmissionsFromHeat")}>
				<HeatContent
					updateHeatEmission={updateHeatEmission}
					heatRows={heatRows}
					setHeatRows={setHeatRows}
					period={data.period}
				/>
			</DisclosureComponent>
			{/* Assuming 'ElectricTransportation' is the correct key or using the string directly if no key exists */}
			<DisclosureComponent title={t("ElectricTransportation")}>
				<ElectricTransportationContent
					updateElectricTransportEmission={updateElectricTransportEmission}
					electricTransportRows={electricTransportRows}
					setElectricTransportRows={setElectricTransportRows}
					PeriodYear={data.PeriodYear}
				/>
			</DisclosureComponent>
			<DisclosureComponent title={t("OtherScope2Emissions")}>
				<OtherScope2Content
					otherScope2Rows={otherScope2Rows}
					setOtherScope2Rows={setOtherScope2Rows}
					updateOtherScope2Emission={updateOtherScope2Emission}
				/>
			</DisclosureComponent>
		</div>
	)
}

export default Scope2Disclosures
