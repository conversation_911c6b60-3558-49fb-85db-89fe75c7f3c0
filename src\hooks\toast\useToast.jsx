import React from "react"
import { toast } from "react-toastify"
//import '../styles/Toast.css';

const useToast = () => {
	let message = "Opps something went wrong!"
	const successContainer = () => (
		<div>
			{message}
			<style>
				{`
					.Toastify__toast-container {
						width: 230px;
						font-size: 12px;
					}
				`}
			</style>
		</div>
	)
	const errorContainer = () => (
		<div>
			{message}
			<style>
				{`
					.Toastify__toast-container {
						width: 230px;
						font-size: 12px;
					}
				`}
			</style>
		</div>
	)
	const showToast = (type, text = "Ops! something went wrong ") => {
		message = text
		switch (type) {
			case "success":
				toast.success(successContainer)
				break
			case "error":
				toast.error(errorContainer)
				break
			default:
				toast.success("TESTING")
		}
	}

	return showToast
}

export default useToast
