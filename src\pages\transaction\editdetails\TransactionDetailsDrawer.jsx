/* eslint-disable import/no-unresolved */
import { <PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON> } from "@headlessui/react"
import { XMarkIcon } from "@heroicons/react/24/outline" // Assuming Heroicons for the X icon
import React, { useState, useCallback } from "react"
// Import actual disclosure components
import Select from "react-select"
//import { v1 as uuidV1 } from "uuid"

import Scope1Disclosures from "./Scope1Disclosures"
import { PERIOD_LIST_ELECTRICITY } from "./scope2/constants/electricTransportation"
import Scope2Disclosures from "./Scope2Disclosures"
import Scope3Disclosures from "./Scope3Disclosures"
import "../../../styles/transaction.css"

// eslint-disable-next-line import/no-unresolved
import Button from "@/components/ui/Button"
import { useToast } from "@/hooks"

// Placeholder for the Emissions Table
const EmissionsTable = ({ data, t }) => {
	const { Scope_1, Scope_2, Scope_3 } = data
	//const { Scope_1: oldScope1, Scope_2: oldScope2, Scope_3: oldScope3 } = oldScopeValues

	//const showOldValues = Scope_1 === 0 && Scope_2 === 0 && Scope_3 === 0

	const displayScope1 = Scope_1
	const displayScope2 = Scope_2
	const displayScope3 = Scope_3

	return (
		<div className='hidden lg:block border rounded-lg shadow-sm'>
			{/* Table structure based on the image */}

			<h2 className=' px-4 py-2 m-0 text-base'>{t("EmissionsForThisTransaction")}</h2>

			<table className='min-w-full divide-y divide-slate-300'>
				<thead className=''>
					<tr>
						<th className='px-4 text-left text-base font-semibold text-slate-900 '>Scope</th>
						<th className='px-4 text-right text-base font-semibold text-slate-900 '>Emissions(Co2e)</th>
					</tr>
				</thead>
				<tbody className='divide-y divide-slate-200 bg-white'>
					{/* Display values based on whether data has been updated */}
					<tr className='table-light'>
						<td className='px-4 py-3 text-left text-sm font-semibold text-slate-700'>Scope 1</td>
						<td className='px-4 py-3 text-right text-sm font-semibold text-rose-700'>
							{Number(displayScope1 || 0.0).toFixed(2)} kg
						</td>
					</tr>
					<tr className='table-light'>
						<td className='px-4 py-3 text-left text-sm font-semibold text-slate-700'>Scope 2</td>
						<td className='px-4 py-3 text-right text-sm font-semibold text-sky-700'>
							{Number(displayScope2 || 0.0).toFixed(2)} kg
						</td>
					</tr>
					<tr className='table-light'>
						<td className='px-4 py-3 text-left text-sm font-semibold text-slate-700'>Scope 3</td>
						<td className='px-4 py-3 text-right text-sm font-semibold text-amber-700'>
							{Number(displayScope3 || 0.0).toFixed(2)} kg
						</td>
					</tr>
				</tbody>

				<tfoot>
					<tr className='border-t-2 border-slate-300'>
						<td className='px-4 py-2 text-left text-base font-semibold text-slate-900'>{t("Total")}</td>
						<td className='px-4 py-2 text-right text-base font-semibold text-slate-900'>
							{(Number(displayScope1 || 0.0) + Number(displayScope2 || 0.0) + Number(displayScope3 || 0.0)).toFixed(2)}{" "}
							kg
						</td>
					</tr>
				</tfoot>
			</table>
		</div>
	)
}

const TransactionDetailsDrawer = ({
	isOpen,
	transaction,
	setShowEditDetailsDialog,
	updateScopeValue,
	t,
	showLoader,
	hideLoader,
}) => {
	const [data, setData] = useState(() => {
		const {
			Description,
			Scope_3_Category,
			PeriodYear,
			Period,
			scope1CombustionRows,
			scope1VolumeRows,
			scope1VehicleRows,
			scope1FugitiveRows,
			scope1OtherRows,
			Scope_1,
			Scope_2,
			Scope_3,
			scope2ElectricityRows,
			scope2HeatRows,
			scope2OtherEmissionRows,
			scope2ElectricTransportRows,
		} = transaction || {}

		return {
			stationaryCombustion: 0.0,
			mobileCombustion: 0.0,
			renewable_energy: 0.0,
			nuclear: 0.0,
			non_renewable_energy: 0.0,
			marketBased: 0.0,
			locationBased: 0.0,
			consumptionBased: 0.0,
			Description: Description || "",
			Scope_3_Category: Scope_3_Category || 0,
			kwh: 0,
			scope1CombustionRows: scope1CombustionRows ? [...scope1CombustionRows] : [],
			scope1FugitiveRows: scope1FugitiveRows ? [...scope1FugitiveRows] : [],
			scope1VehicleRows: scope1VehicleRows ? [...scope1VehicleRows] : [],
			scope1VolumeRows: scope1VolumeRows ? [...scope1VolumeRows] : [],
			scope1OtherRows: scope1OtherRows ? [...scope1OtherRows] : [],
			scope2ElectricityRows: scope2ElectricityRows ? [...scope2ElectricityRows] : [],
			scope2HeatRows: scope2HeatRows ? [...scope2HeatRows] : [],
			scope2OtherEmissionRows: scope2OtherEmissionRows ? [...scope2OtherEmissionRows] : [],
			scope2ElectricTransportRows: scope2ElectricTransportRows ? [...scope2ElectricTransportRows] : [],
			PeriodYear: PeriodYear || "2024",
			Period: Period || 1,
			Scope_1: Scope_1 ? Scope_1 : 0,
			Scope_2: Scope_2 ? Scope_2 : 0,
			Scope_3: Scope_3 ? Scope_3 : 0,
		}
	})

	const toast = useToast()

	const [scope, setScope] = useState(transaction?.Scope || 1)

	const getInitialTabIndex = () => {
		if (!transaction || !transaction.Scope) return 0
		switch (transaction.Scope) {
			case 1:
				return 0
			case 2:
				return 1
			case 3:
				return 2
			default:
				return 0
		}
	}
	const [selectedIndex, setSelectedIndex] = useState(getInitialTabIndex())

	const updateData = useCallback((newObj) => {
		setData((prevState) => ({ ...prevState, ...newObj }))
	}, [])

	const handlePeriodChange = useCallback(
		(selectedOption) => {
			updateData({ Period: selectedOption.value })
		},
		[updateData]
	)

	const getTabDisabled = (tabScope) => {
		return (
			scope &&
			scope !== tabScope &&
			(data.Scope_2 !== 0 || data.Scope_3 !== 0 || transaction?.isEmploeeCommutingTransaction)
		)
	}

	const closeDrawer = () => {
		setShowEditDetailsDialog(false)
		let isNewTransaction = transaction?.isNewTransaction || false
		let id = transaction.TransactionID
		updateScopeValue(id, [], isNewTransaction, true)
	}

	// Placeholder data - replace with actual transaction data
	const transactionId = transaction?.TransactionID || "TR-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"

	const handleSaveValues = () => {
		if (data.Scope_1 === 0 && data.Scope_2 === 0 && data.Scope_3 === 0) {
			toast("error", t("scope_error"))
			return
		}
		//setShowEditDetailsDialog(false)

		let newtransaction = {
			...transaction,
			...data,
			Scope: scope,
		}

		//console.log(newtransaction)

		//console.log(newtransaction)
		updateScopeValue(transaction._id, { ...newtransaction }, transaction.isNewTransaction)
	}

	if (!isOpen) return null

	return (
		<div className='fixed right-0 top-0 bottom-0 w-3/4 max-w-4xl z-[50] bg-white shadow-2xl p-6 pt-4 flex flex-col h-screen'>
			{/* Header */}
			<div className='flex justify-between items-center mb-4'>
				<h2 className='text-lg font-semibold'>
					{t("editDetails")} {transactionId}
				</h2>
				<button
					onClick={() => closeDrawer()}
					className='p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'
					aria-label='Close panel'
				>
					<XMarkIcon className='h-6 w-6 text-gray-600' />
				</button>
			</div>

			{/* Scope Description and Form */}
			<div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-6'>
				<div className='md:col-span-2 space-y-4'>
					<div className='bg-blue-100 border border-blue-300 text-blue-800 p-4 rounded-md text-sm'>
						{t(`TabExplainer Scope ${selectedIndex + 1}`)}
					</div>
					<div>
						<label htmlFor='description' className='block text-sm font-medium text-gray-700 mb-1'>
							{t("Description")}
						</label>
						<textarea
							id='description'
							name='description'
							rows='4'
							value={data.Description} // Use value prop for controlled component
							onChange={(e) => updateData({ Description: e.target.value })}
							className='shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border border-gray-300 rounded-md p-2'
						/>
					</div>
					<div className='flex items-center justify-start'>
						<div className='flex items-center'>
							<label className='metric-label' htmlFor='period'>
								Period
							</label>
							<Select
								id='period'
								className='period-select mx-2'
								value={PERIOD_LIST_ELECTRICITY.find((item) => item.value == parseInt(transaction?.Period))}
								onChange={handlePeriodChange}
								options={PERIOD_LIST_ELECTRICITY}
								maxMenuHeight={300}
							/>
						</div>
						<div className='flex items-center mr-2'>
							<label className='metric-label px-2' htmlFor='period'>
								{transaction?.PeriodYear || "2024"}
							</label>
						</div>
					</div>
				</div>
				<div className='md:col-span-1'>
					<EmissionsTable data={data} t={t} />
				</div>
			</div>

			{/* TabList Fixed + TabPanel Scrollable */}
			<TabGroup
				className='container mb-3 scope-details-container'
				selectedIndex={selectedIndex}
				onChange={setSelectedIndex}
			>
				{/* Adjust top based on total header + content height */}
				<TabList className='flex gap-4 border-b-2 border-sky-500'>
					<Tab
						disabled={getTabDisabled(1)}
						className='py-1 px-3 rounded-tr-lg rounded-tl-lg text-sm/6 font-semibold text-gray-500 data-[selected]:text-white data-[selected]:bg-sky-500  data-[selected]:outline-none'
					>
						Scope 1
					</Tab>
					<Tab
						disabled={getTabDisabled(2)}
						className='py-1 px-3 rounded-tr-lg rounded-tl-lg text-sm/6 font-semibold text-gray-500 data-[selected]:text-white data-[selected]:bg-sky-500  data-[selected]:outline-none'
					>
						Scope 2
					</Tab>
					<Tab
						disabled={getTabDisabled(3)}
						className='py-1 px-3 rounded-tr-lg rounded-tl-lg text-sm/6 font-semibold text-gray-500 data-[selected]:text-white data-[selected]:bg-sky-500  data-[selected]:outline-none'
					>
						Scope 3
					</Tab>
				</TabList>

				{/* Scrollable TabPanel Content */}
				<div className='tab-content overflow-auto'>
					<TabPanels>
						<TabPanel className='rounded-xl bg-white/5'>
							<Scope1Disclosures updateData={updateData} data={data} t={t} setScope={setScope} />
						</TabPanel>
						<TabPanel className='rounded-xl bg-white/5'>
							<Scope2Disclosures updateData={updateData} data={data} t={t} setScope={setScope} />
						</TabPanel>
						<TabPanel className='rounded-xl bg-white/5'>
							<Scope3Disclosures
								updateData={updateData}
								data={data}
								t={t}
								showLoader={showLoader}
								hideLoader={hideLoader}
								setScope={setScope}
								isEmploeeCommutingTransaction={transaction?.isEmploeeCommutingTransaction || false}
							/>
						</TabPanel>
					</TabPanels>
				</div>
			</TabGroup>

			{/* Footer */}
			<div className='fixed right-0 bottom-0 w-3/4 max-w-4xl bg-white z-60 p-2 border-t border-gray-200 flex justify-end space-x-3'>
				<Button title={t("Cancel")} variation='danger' handleClick={() => closeDrawer()} />
				<div className='!mr-5'>
					<Button title={t("Save")} handleClick={() => handleSaveValues()} />
				</div>
			</div>
		</div>
	)
}

export default TransactionDetailsDrawer
