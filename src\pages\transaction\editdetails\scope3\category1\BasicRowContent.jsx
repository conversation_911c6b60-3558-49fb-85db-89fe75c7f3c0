/* eslint-disable import/no-unresolved */
import React, { useState } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"

import Button from "@/components/ui/Button" // Assuming alias setup
import Input from "@/components/ui/Input" // Assuming alias setup
import { useToast } from "@/hooks"

function BasicRowContent(props) {
	// Props expected:
	// - category1BasicRows: Array of existing emission objects [{ metric, factor, unit, scope1, scope2, scope3 }, ...]
	// - setCategory1BasicRows: Function to update the array of emission objects in the parent state
	// - updateCategory1BasicEmission: Optional function to notify parent of changes (e.g., for backend updates)
	const { category1BasicRows = [], setCategory1BasicRows, updateCategory1BasicEmission } = props
	const { t } = useTranslation("transactions")
	const toast = useToast()

	// Local state for the "Add New" form inputs
	const [metric, setMetric] = useState("") // Corresponds to 'Description' in the original input
	const [factor, setFactor] = useState(0.0)
	const [unit, setUnit] = useState(0.0)

	// Adds the new row data (from local state) to the main list (parent state)
	const addCategory1BasicRow = (calculatedScope3) => {
		const newCategory1BasicRow = {
			metric: metric,
			factor: Number(factor),
			unit: Number(unit),
			scope1: 0, // Assuming this category is Scope 3
			scope2: 0,
			Scope_3_Category: 1,
			scope3: Number(calculatedScope3), // Calculated emission
		}

		const newCategory1BasicRowsList = [...category1BasicRows, newCategory1BasicRow]
		setCategory1BasicRows(newCategory1BasicRowsList)

		// Optionally notify parent about the addition
		if (updateCategory1BasicEmission) {
			updateCategory1BasicEmission(newCategory1BasicRow, false) // false indicates addition
		}

		// Reset form fields
		setMetric("")
		setFactor(0.0)
		setUnit(0.0)
	}

	// Calculates emission and triggers adding the row
	const handleAddEmission = () => {
		// Basic validation
		if (metric.trim() === "") {
			toast("error", t("discription_error"))
			return
		}

		if (parseFloat(factor) <= 0 && parseFloat(unit) <= 0) {
			toast("error", t("factor_unit_greater_0_error"))
			return
		}

		// Assuming the emission calculation for Scope 3 Category 1 is factor * unit
		let calculatedScope3 = parseFloat(factor) * parseFloat(unit)
		addCategory1BasicRow(calculatedScope3)
	}

	// Deletes a row from the list by index
	const deleteCategory1BasicRow = (index) => {
		const rowToDelete = category1BasicRows[index]
		const newCategory1BasicRowsList = [...category1BasicRows]
		newCategory1BasicRowsList.splice(index, 1)

		setCategory1BasicRows(newCategory1BasicRowsList)

		// Optionally notify parent about the deletion
		if (updateCategory1BasicEmission) {
			updateCategory1BasicEmission(rowToDelete, true) // true indicates deletion
		}
	}

	const handleFocus = (event) => event.target.select()

	return (
		<>
			{/* Display existing/saved rows */}
			{category1BasicRows.map((category1Basic, index) => (
				<div key={index}>
					{/* Using the display structure from the original component's 'isSaved' block */}
					<div className='grid grid-cols-5 gap-10 my-1 saved-emission'>
						<div>
							{/* Label changed from "Metric" to "Description" based on original input label */}
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Description")}</label>
							<div>{category1Basic.metric}</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Factor")}</label>
							<div className='flex relative'>
								<span>{category1Basic.factor}</span>
								{/* Unit from original saved display */}
								<span className='custom-span-unit-value-save'>{t("Co2e")}</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Units")}</label>
							<div className='flex relative'>
								<span>{category1Basic.unit}</span>
								{/* Unit from original saved display */}
								<span className='custom-span-unit-value-save'>{t("Pcs")}</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("TotalCo2e")}</label>
							<div className='flex relative'>
								{/* Calculate total based on available scopes */}
								<span>
									{Number(
										(category1Basic.scope1 || 0) + (category1Basic.scope2 || 0) + (category1Basic.scope3 || 0)
									).toFixed(2)}
								</span>
								<span className='text-nowrap custom-span-unit-value-save'>{t("kg")}</span>
							</div>
						</div>

						<div className='!items-center delete-icon'>
							<span aria-hidden onClick={() => deleteCategory1BasicRow(index)} style={{ cursor: "pointer" }}>
								<RiDeleteBin6Line />
							</span>
						</div>
					</div>
					<hr />
				</div>
			))}

			{/* Form for adding new rows */}
			<div className='grid grid-cols-4 gap-10 my-2'>
				{" "}
				{/* Adjusted grid columns for the form */}
				<div>
					<Input
						label={t("Description")} // Label from original component's input
						type='text'
						labelColor='text-sky-500'
						value={metric}
						handleChange={(e) => setMetric(e.target.value)}
						handleFocus={handleFocus}
						placeholder={t("Enter description")} // Added placeholder
					/>
				</div>
				<div>
					<Input
						label={t("Factor")}
						labelColor='text-sky-500'
						type='number'
						value={factor}
						handleChange={(e) => setFactor(e.target.value)}
						handleFocus={handleFocus}
						unit={t("Co2e")} // Unit from original component's input
						min='0' // Ensure non-negative
						step='any' // Allow decimals
					/>
				</div>
				<div>
					<Input
						label={t("Units")}
						labelColor='text-sky-500'
						type='number'
						value={unit}
						handleChange={(e) => setUnit(e.target.value)}
						handleFocus={handleFocus}
						unit={t("Pcs")} // Unit from original component's input
						min='0' // Ensure non-negative
						step='any' // Allow decimals
					/>
				</div>
				<div className='self-end mb-1'>
					<Button title={`${t("Add")} | +`} handleClick={handleAddEmission} color='sky-500' />
				</div>
			</div>
		</>
	)
}

export default BasicRowContent
