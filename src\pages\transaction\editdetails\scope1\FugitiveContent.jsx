/* eslint-disable import/no-unresolved */
import React, { useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"
import Select from "react-select"

import { FUGITIVE_GASES_DATA, METRIC_LIST_FUGITIVE } from "./constants/fugitive"

import Button from "@/components/ui/Button"
import Input from "@/components/ui/Input"
import { useToast } from "@/hooks"

function FugitiveContent(props) {
	const { fugitiveRows, updateFugitiveScope } = props
	const unitInputRef = useRef()
	const { t } = useTranslation("transactions")
	const toast = useToast()

	const [fugitiveFactors, setFugitiveFactors] = useState({})

	const [metric, setMetric] = useState(METRIC_LIST_FUGITIVE[0])
	const [label, setLabel] = useState(METRIC_LIST_FUGITIVE[0].label)
	const [factor, setFactor] = useState(0.0)
	const [unit, setUnit] = useState(0.0)

	const getFactor = (metric) => {
		return fugitiveFactors[metric.value]
	}

	const metricChange = (event) => {
		setMetric(event)
		setLabel(event.label)
		setFactor(Number(getFactor(event)).toFixed(2))

		unitInputRef.current.focus()
	}

	const addFugitiveRow = (scope1) => {
		const newFugitiveRow = {
			metric: label,
			factor: Number(factor),
			unit: Number(unit),
			unitType: metric.unit,
			scope1: Number(Number(scope1).toFixed(2)),
			scope2: 0,
			scope3: 0,
		}

		const newFugitives = [...fugitiveRows, newFugitiveRow]

		updateFugitiveScope(newFugitiveRow, false, newFugitives)
	}

	const calculateFugitiveEmission = () => {
		if (parseFloat(factor) <= 0 || parseFloat(unit) <= 0) {
			toast("error", t("factor_unit_greater_0_error"))
			return
		}

		let scope1 = factor * unit

		addFugitiveRow(scope1)
	}

	const deleteFugitiveRow = (index) => {
		let fugitiveRow = fugitiveRows[index]
		let newFugitiveRows = [...fugitiveRows]
		newFugitiveRows.splice(index, 1)

		updateFugitiveScope(fugitiveRow, true, newFugitiveRows)
	}

	useEffect(() => {
		const fugitiveFactorData = {}

		for (const { gasType, co2e } of FUGITIVE_GASES_DATA) {
			fugitiveFactorData[gasType] = co2e
		}

		setFugitiveFactors(fugitiveFactorData)
	}, [])

	const handleFocus = (event) => event.target.select()

	return (
		<>
			{fugitiveRows.map((fugitive, index) => (
				<div key={index}>
					<div className='grid grid-cols-5 gap-10 my-1 saved-emission'>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Metric")}</label>
							<div>{fugitive.metric}</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Factor")}</label>
							<div className='flex relative'>
								<span>{fugitive.factor}</span>
								<span className='custom-span-unit-value-save'>
									{t("Gram")} {t("Co2e")}
								</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Units")}</label>
							<div className='flex relative'>
								<span>{fugitive.unit}</span>
								<span className='text-nowrap custom-span-unit-value-save'>{fugitive.unitType}</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("TotalCo2e")}</label>
							<div className='flex relative'>
								<span>{Number(fugitive.scope1 + fugitive.scope2 + fugitive.scope3).toFixed(2)}</span>
								<span className='text-nowrap custom-span-unit-value-save'>{t("kg")}</span>
							</div>
						</div>

						<div className='!items-center delete-icon'>
							<span aria-hidden onClick={() => deleteFugitiveRow(index)}>
								<RiDeleteBin6Line />
							</span>
						</div>
					</div>
					<hr />
				</div>
			))}

			<div className='grid grid-cols-4 gap-10'>
				<div>
					<label className='text-slate-800 font-semibold whitespace-nowrap' htmlFor={"metric-processAndFugitive-"}>
						{t("Metric")}
					</label>
					<Select
						id={"metric-processAndFugitive-"}
						value={metric}
						onChange={metricChange}
						options={METRIC_LIST_FUGITIVE}
						maxMenuHeight={150}
					/>
				</div>

				<div>
					<Input
						type='number'
						labelColor='text-sky-500'
						label={t("Factor")}
						value={factor}
						handleChange={(e) => setFactor(e.target.value)}
						handleFocus={handleFocus}
						unit={t("Kg-Co2e")}
					/>
				</div>
				<div>
					<Input
						label={t("Units")}
						labelColor='text-sky-500'
						value={unit}
						handleChange={(e) => setUnit(e.target.value)}
						handleFocus={handleFocus}
						type='number'
						ref={unitInputRef}
						unit={metric.unitType}
					/>
				</div>
				<div className='self-end mb-1'>
					<Button title={`${t("Add")} | +`} handleClick={() => calculateFugitiveEmission()} color='sky-500' />
				</div>
			</div>
		</>
	)
}

export default FugitiveContent
