{"discription_error": "Description cannot be empty.", "factor_unit_greater_0_error": "Factor and Unit must be greater then zero.", "factor_unit_weight_greater_0_error": "Factor, Units, or Weight Per Unit cannot be zero, empty or negitive", "transport_type_error": "Please select a Transport Type.", "factor_weight_distance_greater_0_error": "Factor, Weight, and Distance can not be empty, zero or negitive number.", "vehicle_type_error": "Please select a Vehicle Type.", "continue": "CONTINUE", "cancel": "CANCEL", "deletion_warning": "Deletion Warning", "apply_filter": "Apply Filter", "clear_filter": "Clear Filter", "projects": "Projects", "departments": "Departments", "confirm_delete_message": "Are you sure you want to delete this?", "account_id_updated_message": "Account ID updated successfully.", "error_updating_account_id_message": "Sorry, we have an error while updating Account ID.", "transactions_updated_message": "Transactions are updated successfully.", "error_updating_multiple_transactions_message": "Error while updating multiple transactions.", "supplier_empty_message": "Supplier shouldn't be empty, please add the transaction again.", "amount_empty_message": "Amount shouldn't be empty, please add the transaction again or enter 0.", "transaction_updated_message": "Transaction is updated successfully.", "error_updating_transaction_message": "Sorry, we have an issue while updating the transaction.", "no_transaction_selected_message": "No transaction is selected.", "rows_copied_message": "Selected rows are copied to clipboard.", "transaction_amount_missing_message": "There is a transaction selected in which amount is not available.", "transactions_deleted_message": "Transactions deleted successfully.", "error_deleting_transactions_message": "Sorry, we have an error while deleting transactions.", "error_adding_transaction_message": "Sorry, we have an error while adding a new transaction.", "transaction_added_message": "Transaction added successfully.", "error_updating_message": "Sorry, we have an error while updating.", "emission_updated_message": "Emission updated successfully.", "Category8Info": "This is for calculating energy and emissions if you are not paying energy bills and report on this in scope 1 and scope 2", "Distanse": "<PERSON><PERSON><PERSON>", "People": "People", "Size": "Size", "TravelMode": "Travel Mode", "Drivetrain": "Drivetrain", "addTransactionHeading": "Add New Record", "update_multiple_transactions": "Update Multiple Transactions", "update_account_id": "Update Account ID", "delete_records": "Delete Records", "multiple_transactions_heading": "Edit multiple transactions", "Yearly": "Yearly", "Monthly": "Monthly", "Quarterly": "Quarterly", "Barnehage": "Kindergarten", "Kontor": "Office", "Skole": "School", "Universitet": "University", "Sykehus": "Hospital", "Sykehjem": "Nursing Home", "Hotell": "Hotel", "Idrettsbygg": "Sports Building", "Forretning": "Business", "Kulturbygning": "Cultural Building", "Lett industri": "Light Industry", "ControlledTooltip": "A controlled building/asset is one where you are responsible for paying energy bills and report on this in Scope 1 or scope 2.", "Scope 3 Category": "Scope 3 category", "EmissionsFromElectricitytooltip": "For electricity the factor is set to '0' for electricity purchased with Guarantees of Origin (GOs). Without GOs a company should refer to the electricity disclosure published by NVE. It is possible to use the European mix factor and the factor can be modified if you want to use another factor. Remember to comment on the choice in your report.", "InputEmmissionManually": "Input emmision manually", "SelectEmission": "Select an emission factor to be used", "Supplier_list_empty": "Suppliers list is empty please add supplier first", "add_supplier": "Add Supplier", "Amount_zero": "Amount should not be 0.", "Scope_3_Category": "Scope 3 Category", "MultipleEdit": "You are editing multiple transactions. Changes will apply to all transactions that are selected. Scope 1, 2, and 3 will be distributed across the selected transactions.", "Add": "Add", "Edit": "Edit", "Delete": "Delete", "LineNumber": "Line #", "RecordID": "Record Id", "AccountID": "Account Id", "SupplierID": "Supplier Id", "TransactionID": "Transaction Id", "Description": "Description", "CreditAmount": "Credit amount", "DebitAmount": "Debit amount", "ReferenceNumber": "Reference number", "SupplierName": "Supplier name", "Emissions-kgCo2e": "Emissions (kgCo2e)", "Status": "Status", "AccountDescription": "Account description", "Period": "Period", "PeriodYear": "Period year", "TransactionDate": "Transaction date", "NaceCode": "NaceCode", "Notes": "Notes", "RedFlagMsg": "This transaction has been flagged as red. You should probably edit the details of this.", "EditDetails": "Edit Details", "EditTransactionId": "Edit transactionId", "EmissionsForThisTransaction": "Emissions for this transaction", "Scope": "<PERSON><PERSON>", "Scope1": "Scope 1", "Scope2": "Scope 2", "Scope3": "Scope 3", "Emissions-Co2e": "Emissions(Co2e)", "kg": "kg", "Total": "Total", "EmissionsFromCombustion": "Emissions from combustion", "EmissionsFromVehicles": "Emissions from vehicles", "ProcessAndFugitiveEmissions": "Process and fugitive emissions", "OtherScope1Emissions": "Other scope 1 emissions", "EmissionsFromElectricity": "Emissions from electricity", "EmissionsFromHeat": "Emissions from heat", "OtherScope2Emissions": "Other scope 2 emissions", "Category1-PurchasedGoodsAndServices": "Category 1 - Purchased Goods And Services", "Category2-CapitalGoods": "Category 2 - Capital Goods", "Category3-FuelAndEnergyRelatedActivities": "Category 3 - Fuel and Energy Related Activities", "Category4-UpstreamTransportationAndDistribution": "Category 4 - Upstream Transportation and Distribution", "Category5-WasteGeneratedInOperations": "Category 5 - Waste Generated in Operations", "Category6-BusinessTravel": "Category 6 - Business Travel", "Category7-EmployeeCommuting": "Category 7 - Employee Commuting", "Category8-UpstreamLeasedAssets": "Category 8 - Upstream leased assets", "Category9-DownstreamTransportationandDistribution": "Category 9 - Downstream Transportation and Distribution", "Category10-ProcessingofSoldProducts": "Category 10 - Processing of Sold Products", "Category11-UseofSoldProducts": "Category 11 - Use of Sold Products", "Category12-EndofLifeTreatmentofSoldProducts": "Category 12 - End-of-Life Treatment of Sold Products", "Category13-DownstreamLeasedAssets": "Category 13 - Downstream Leased Assets", "Category14-Franchises": "Category 14 - Franchises", "Category15-Investments": "Category 15 - Investments", "Save": "Save", "Cancel": "Cancel", "Metric": "Metric", "Factor": "Factor", "Unit": "Unit", "Kg-Co2e": "Kg Co2e", "Units": "Units", "OtherEmissionSources": "Other Emission Sources", "Gram": "Gram", "Co2e": "Co2e", "kwh": "kwh", "TotalCo2e": "Total Co2e", "VehicleType": "Vehicle Type", "FuelType": "Fuel Type", "Distance": "Distance", "Economy": "Economy", "BasicInput": "Basic Input", "Pcs": "Pcs", "TransportType": "Transport Type", "Weight": "Weight", "MaterialType": "Material Type", "RecycledShare": "Recycled Share", "Flights": "Flights", "LandTransport": "Land Transport", "Accommodation": "Accommodation", "AccommodationType": "Accommodation Type", "FlightType": "Flight Type", "From": "From", "Destination": "Destination", "FlightClass": "Flight Class", "Traveller": "Traveller", "SearchAirport": "Search Airport", "AddRadiativeFactor": "Add Radiative Factor", "BuildingType": "Building Type", "Yes": "Yes", "No": "No", "Country": "Country", "Number": "Number", "Nights": "Nights", "kgs": "kgs", "tkm": "tkm", "Scope3Category": "Scope 3 Category", "EmissionSourceRegistration": "Activity-based record", "PurchaseWithKnownSupplierAmount": "Spend-based record", "TransactionWithKnownEmission": "Record with a known emission", "ProductionRelatedPurchase": "Production related purchase", "NonProductionRelatedPurchase": "Non-production related purchase", "Recalculation": "Recalculation", "RecalculationMsg": "Scope 1, 2 and 3 above must be 0 for recalculation to be executed", "SupplierFactor": "Supplier’s factor", "CustomEmissionFactor": "Custom emission factor", "NaceCode-Industry": "Nace Code - Industry", "SearchByIndustryOrNaceCode": "Search By Industry Or Nace Code", "fuel_type_error": "Please select a Fuel Type.", "distance_error": "Distance cannot be negative or empty.", "economy_heavy_truck_error": "Economy cannot be empty for Heavy Truck.", "economy_negative_error": "Economy cannot be negative.", "TabExplainer Scope 1": "This is scope1 - Direct emissions. Fossil fuel combustion in production and from own vehicles. You can also specify processemissions that you measure separately. Fugitive emissions are emissions of gases or vapors from pressurized equipment due to leaks.", "TabExplainer Scope 2": "Scope 2 - Indirect emisssions from purchased electricity, heat, and cooling. Add the kilowatt hours for the transaction", "TabExplainer Scope 3": "These are scope 3 indirect emisssions from your upstream value chain. The GHG-protocol group these into 8 categories as below. When accounting for employees commute you first need to add a transaction.", "Strøm med opprinnelsesgaranti": "With a certificate of origin", "Norsk residual mix(uten OG)": "Without a certificate of origin", "European Mix": "European Mix", "Other": "Other", "material_type_error": "Please select a Material Type.", "weight_error": "Weight cannot be negative or empty.", "recycled_share_error": "Recycled Share must be between 0 and 100.", "from_airport_error": "Please select From airport.", "destination_airport_error": "Please select Destination airport.", "traveller_count_error": "Traveller count must be greater than 0.", "travel_mode_error": "Please select a Travel Mode.", "select_type_error": "Please select a Type.", "drivetrain_error": "Please select a Drivetrain.", "people_count_error": "People count must be greater than 0.", "accommodation_type_error": "Please select an Accommodation Type.", "country_error": "Please select a Country.", "number_nights_error": "Number of nights must be greater than 0.", "building_type_error": "Please select a Building Type.", "period_type_error": "Please select a Type (Yearly/Monthly/Quarterly).", "size_error": "Size cannot be negative or empty.", "material_level3_disposal_error": "Please select Material Type, Level 3, and Disposal Method.", "factor_weight_non_negative_error": "Factor and Weight must be non-negative numbers.", "flight_details_error": "Category 6 flight emission's from, destination or traveller can't be empty.", "select_all_fields": "Please fill all the fields", "select_drive_train": "Please select drive train", "category_8_error": "Category eight emission's factor or size can't be empty or zero.", "vehicle_distance_error": "Vehicles distance can't be empty.", "electricity_error": "Electricity Factor, Unit and Period can't be empty.", "factor_distance_error": "factor and distance can't be empty or zero.", "heat_error": "Heat Factor, Unit and Period can't be empty or zero.", "scope_error": "Please add atleast one scope value", "editDetails": "Edit details of"}