import { useQuery } from "@tanstack/react-query"

const getCategory5FactorApiData = async () => {
	try {
		// const response = await fetch("/.netlify/functions/getCategory5Factors", {
		// 	method: "POST",
		// 	headers: {
		// 		"Content-Type": "application/json",
		// 	},
		// })
		// const data = await response.json()
		//return data // Ensure you return res.data or another relevant part of the response
	} catch (error) {
		throw new Error("Failed to fetch category 5 API data")
	}
}

const useGetCategory5FactorApiData = () => {
	return useQuery({
		queryKey: ["Category5FactorAPIData"],
		queryFn: getCategory5FactorApiData,
	})
}

export default useGetCategory5FactorApiData
