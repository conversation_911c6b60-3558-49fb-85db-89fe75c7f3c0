import { Tab, <PERSON>bGroup, <PERSON>b<PERSON><PERSON>, Tab<PERSON>anel, TabPane<PERSON> } from "@headlessui/react"
import React, { useState } from "react"

import DisclosureComponent from "./DisclosureComponent"
import CombustionContent from "./scope1/CombustionContent"
import FugitiveContent from "./scope1/FugitiveContent"
import OtherScope1Content from "./scope1/OtherScope1Content"
import VehiclesContent from "./scope1/VehiclesContent"
import VolumeContent from "./scope1/VolumeContent"

const Scope1Disclosures = ({ updateData, data, t, setScope }) => {
	const { scope1CombustionRows, scope1VolumeRows, scope1VehicleRows, scope1FugitiveRows, scope1OtherRows } = data

	const [combustionRows, setCombustionRows] = useState([...scope1CombustionRows])
	const [vehicleRows, setVehicleRows] = useState([...scope1VehicleRows])
	const [volumeRows, setVolumeRows] = useState([...scope1VolumeRows])
	const [fugitiveRows, setFugitiveRows] = useState([...scope1FugitiveRows])
	const [otherScope1Rows, setOtherScope1Rows] = useState([...scope1OtherRows])

	const updateScopeAndCombustion = async (newRow, isDeleted = false, updatedCombustions) => {
		console.log(updatedCombustions.length)

		let {
			stationaryCombustion = 0,
			kwh = 0,
			Scope_1 = 0,
			Scope_2 = 0,
			Scope_3 = 0,
		} = {
			...data,
			stationaryCombustion: Number(data.stationaryCombustion) || 0,
			kwh: Number(data.kwh) || 0,
			Scope_1: Number(data.Scope_1) || 0,
			Scope_2: Number(data.Scope_2) || 0,
			Scope_3: Number(data.Scope_3) || 0,
		}

		if (isDeleted) {
			stationaryCombustion -= newRow.stationary
			kwh -= newRow.kwh
			Scope_1 -= newRow.scope1
			Scope_2 -= newRow.scope2
			Scope_3 -= newRow.scope3
		} else {
			stationaryCombustion += newRow.stationary
			kwh += newRow.kwh
			Scope_1 += parseFloat(newRow.scope1)
			Scope_2 += newRow.scope2
			Scope_3 += newRow.scope3
		}

		setScope(1)

		await updateData({
			stationaryCombustion: parseFloat(Number(stationaryCombustion).toFixed(2)),
			kwh: parseFloat(Number(kwh).toFixed(2)),
			Scope_1: parseFloat(parseFloat(Scope_1).toFixed(2)),
			Scope_2: parseFloat(Number(Scope_2).toFixed(2)),
			Scope_3: parseFloat(Number(Scope_3).toFixed(2)),
			scope1CombustionRows: updatedCombustions,
		})
	}

	const updateVehiclesScope = async (newRow, isDeleted = false, newVehicles) => {
		let {
			mobileCombustion = 0,
			kwh = 0,
			Scope_1 = 0,
			Scope_2 = 0,
			Scope_3 = 0,
			renewable_energy = 0,
			non_renewable_energy = 0,
		} = {
			...data,
			mobileCombustion: Number(data.mobileCombustion) || 0,
			kwh: Number(data.kwh) || 0,
			Scope_1: Number(data.Scope_1) || 0,
			Scope_2: Number(data.Scope_2) || 0,
			Scope_3: Number(data.Scope_3) || 0,
			renewable_energy: Number(data.renewable_energy) || 0,
			non_renewable_energy: Number(data.non_renewable_energy) || 0,
		}

		if (isDeleted) {
			mobileCombustion -= newRow.mobileCombustion
			kwh -= newRow.kwh
			Scope_1 -= newRow.scope1
			Scope_2 -= newRow.scope2
			Scope_3 -= newRow.scope3
			renewable_energy -= newRow.renewable
			non_renewable_energy -= newRow.non_renewable_energy
		} else {
			mobileCombustion += newRow.mobileCombustion
			kwh += newRow.kwh
			Scope_1 += newRow.scope1
			Scope_2 += newRow.scope2
			Scope_3 += newRow.scope3
			renewable_energy += newRow.renewable
			non_renewable_energy += newRow.non_renewable_energy
		}

		setScope(1)
		await updateData({
			mobileCombustion: parseFloat(Number(mobileCombustion).toFixed(2)),
			kwh: parseFloat(Number(kwh).toFixed(2)),
			Scope_1: parseFloat(parseFloat(Scope_1).toFixed(2)),
			Scope_2: parseFloat(Number(Scope_2).toFixed(2)),
			Scope_3: parseFloat(Number(Scope_3).toFixed(2)),
			renewable_energy: parseFloat(Number(renewable_energy).toFixed(2)),
			non_renewable_energy: parseFloat(Number(non_renewable_energy).toFixed(2)),
			scope1VehicleRows: newVehicles,
		})
	}

	const updateVolumeScope = async (newRow, isDeleted = false, newVolumeRows) => {
		let {
			kwh = 0,
			Scope_1 = 0,
			Scope_2 = 0,
			Scope_3 = 0,
			mobileCombustion = 0,
		} = {
			...data,
			kwh: Number(data.kwh) || 0,
			Scope_1: Number(data.Scope_1) || 0,
			Scope_2: Number(data.Scope_2) || 0,
			Scope_3: Number(data.Scope_3) || 0,
			mobileCombustion: Number(data.mobileCombustion) || 0,
		}

		if (isDeleted) {
			mobileCombustion -= newRow.mobileCombustion
			kwh -= newRow.kwh
			Scope_1 -= newRow.scope1
			Scope_2 -= newRow.scope2
			Scope_3 -= newRow.scope3
		} else {
			mobileCombustion += newRow.mobileCombustion
			kwh += newRow.kwh
			Scope_1 += parseFloat(newRow.scope1)
			Scope_2 += newRow.scope2
			Scope_3 += newRow.scope3
		}

		setScope(1)
		await updateData({
			mobileCombustion: parseFloat(Number(mobileCombustion).toFixed(2)),
			kwh: parseFloat(Number(kwh).toFixed(2)),
			Scope_1: parseFloat(parseFloat(Scope_1).toFixed(2)),
			Scope_2: parseFloat(Number(Scope_2).toFixed(2)),
			Scope_3: parseFloat(Number(Scope_3).toFixed(2)),
			scope1VolumeRows: newVolumeRows,
		})
	}

	const updateFugitiveScope = async (newRow, isDeleted = false, newFugitiveRows) => {
		let { Scope_1 = 0 } = {
			...data,
			Scope_1: Number(data.Scope_1) || 0,
		}

		if (isDeleted) {
			Scope_1 -= newRow.scope1
		} else {
			Scope_1 += parseFloat(newRow.scope1)
		}

		setScope(1)
		await updateData({
			Scope_1: parseFloat(parseFloat(Scope_1).toFixed(2)),
			scope1FugitiveRows: newFugitiveRows,
		})
	}

	const updateOtherScope1Emission = async (newRow, isDeleted = false, newOtherScope1Rows) => {
		let { Scope_1 = 0 } = {
			...data,
			Scope_1: Number(data.Scope_1) || 0,
		}

		if (isDeleted) {
			Scope_1 -= newRow.scope1
		} else {
			Scope_1 += parseFloat(newRow.scope1)
		}

		setScope(1)
		await updateData({
			Scope_1: parseFloat(parseFloat(Scope_1).toFixed(2)),
			scope1OtherRows: newOtherScope1Rows,
		})
	}

	return (
		<div className='[&>:not(:first-child)]:border-t-2'>
			{" "}
			{/* Apply border-t-2 to all direct children except the first */}
			<DisclosureComponent isOpen={scope1CombustionRows.length > 0} title={t("EmissionsFromCombustion")}>
				<CombustionContent
					updateScopeAndCombustion={updateScopeAndCombustion}
					combustionRows={combustionRows}
					setCombustionRows={setCombustionRows}
				/>
			</DisclosureComponent>
			<DisclosureComponent
				isOpen={scope1VehicleRows.length > 0 || scope1VolumeRows.length > 0}
				title={t("EmissionsFromVehicles")}
			>
				<TabGroup defaultIndex={scope1VolumeRows.length > 0 ? 1 : 0}>
					<TabList className='flex gap-4 border-b-2 border-sky-500'>
						<Tab className='py-2 px-3 rounded-tr-lg text-sm/6 font-semibold text-gray-500 data-[selected]:text-white data-[selected]:bg-sky-500  data-[selected]:outline-none'>
							{t("Distance")}
						</Tab>

						<Tab className='py-1 px-3 rounded-tr-lg rounded-tl-lg text-sm/6 font-semibold text-gray-500 data-[selected]:text-white data-[selected]:bg-sky-500  data-[selected]:outline-none'>
							{t("Volume")}
						</Tab>
					</TabList>
					<TabPanels className='bg-white mt-5'>
						<TabPanel className='rounded-xl bg-white/5'>
							<VehiclesContent
								vehicleRows={vehicleRows}
								setVehicleRows={setVehicleRows}
								updateVehiclesScope={updateVehiclesScope}
								PeriodYear={data.PeriodYear}
							/>
						</TabPanel>

						<TabPanel className='rounded-xl bg-white/5'>
							<VolumeContent
								updateVolumeScope={updateVolumeScope}
								volumeRows={volumeRows}
								setVolumeRows={setVolumeRows}
							/>
						</TabPanel>
					</TabPanels>
				</TabGroup>
			</DisclosureComponent>
			<DisclosureComponent isOpen={scope1FugitiveRows.length > 0} title={t("ProcessAndFugitiveEmissions")}>
				<FugitiveContent
					updateFugitiveScope={updateFugitiveScope}
					fugitiveRows={fugitiveRows}
					setFugitiveRows={setFugitiveRows}
				/>
			</DisclosureComponent>
			<DisclosureComponent isOpen={scope1OtherRows.length > 0} title={t("OtherScope1Emissions")}>
				<OtherScope1Content
					updateOtherScope1Emission={updateOtherScope1Emission}
					otherScope1Rows={otherScope1Rows}
					setOtherScope1Rows={setOtherScope1Rows}
				/>
			</DisclosureComponent>
		</div>
	)
}

export default Scope1Disclosures
