import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON> } from "@headlessui/react"
import React from "react"

import DisclosureComponent from "./DisclosureComponent"
import BasicRowContent from "./scope3/category1/BasicRowContent"
import MaterialRowContent from "./scope3/category1/MaterialRowContent"
import Scope3Category2Content from "./scope3/category2/Scope3Category2Content"
import Scope3Category4Content from "./scope3/category4/Scope3Category4Content" // Import Category 4 Default
import Scope3Category4VTContent from "./scope3/category4/Scope3Category4VTContent" // Import Category 4 VT
import Scope3Category5Content from "./scope3/category5/Scope3Category5Content" // Import Category 5
import Scope3Category6Accomudation from "./scope3/category6/Scope3Category6Accomudation"
import Scope3Category6Flights from "./scope3/category6/Scope3Category6Flights"
import Scope3Category6LT from "./scope3/category6/Scope3Category6LT"
import Scope3Category7Content from "./scope3/category7/Scope3Category7Content"
import Scope3Category8Content from "./scope3/category8/Scope3Category8Content"

// Placeholder content for each disclosure
const Category3Content = () => <div>---------</div>

const Scope3Disclosures = ({
	updateData,
	data,
	t,
	setScope,
	isEmploeeCommutingTransaction,
	showLoader,
	hideLoader,
}) => {
	const {
		scope3Category1BasicRows,
		scope3Category1MaterialRows,
		scope3Category2Rows,
		scope3Category4Rows,
		scope3Category4VTRows,
		scope3Category5Rows,
		scope3Category6AccommodationRows,
		scope3Category6FlightRows,
		scope3Category6LandTransportRows,
		scope3Category7Rows,
		scope3Category8Rows,
	} = data

	const updateCategory4VTEmission = async (newRow, isDeleted, updatedCategory4VTRows) => {
		let { mobileCombustion, kwh, Scope_1, Scope_2, Scope_3, renewable_energy, non_renewable_energy } = data
		// now in combustion rows we have scope 1,2,3 kwh and stationary keys add all rows and set
		// them above and updateData

		//console.log(stationaryCombustion, kwh, Scope_1, Scope_2, Scope_3)

		if (isDeleted) {
			mobileCombustion -= newRow.mobileCombustion
			kwh -= newRow.kwh
			Scope_1 -= newRow.scope1
			Scope_2 -= newRow.scope2
			Scope_3 -= newRow.scope3
			renewable_energy -= newRow.renewable
			non_renewable_energy -= newRow.non_renewable_energy
		} else {
			mobileCombustion += newRow.mobileCombustion
			kwh += newRow.kwh
			Scope_1 += newRow.scope1
			Scope_2 += newRow.scope2
			Scope_3 += newRow.scope3
			renewable_energy += newRow.renewable
			non_renewable_energy += newRow.non_renewable_energy
		}

		//console.log(Scope_1, newRow.scope1, Scope_1 + parseFloat(newRow.scope1))

		// Format to 2 decimal places and ensure the result is a number

		setScope(3)
		await updateData({
			mobileCombustion: parseFloat(Number(mobileCombustion).toFixed(2)),
			kwh: parseFloat(Number(kwh).toFixed(2)),
			Scope_1: parseFloat(parseFloat(Scope_1).toFixed(2)), // Note: parseFloat already applied before toFixed
			Scope_2: parseFloat(Number(Scope_2).toFixed(2)),
			Scope_3: parseFloat(Number(Scope_3).toFixed(2)),
			renewable_energy: parseFloat(Number(renewable_energy).toFixed(2)),
			non_renewable_energy: parseFloat(Number(non_renewable_energy).toFixed(2)),
			Scope_3_Category: 4,
			scope3Category4VTRows: updatedCategory4VTRows,
		})
	}

	const updateCategory8Emission = async (newRow, isDeleted, updatedCategory8Rows) => {
		let { Scope_3, kwh, renewable_energy, non_renewable_energy, nuclear } = data

		if (isDeleted) {
			Scope_3 -= newRow.scope3
			kwh -= newRow.kwh
			renewable_energy -= newRow.renewable_energy
			non_renewable_energy -= newRow.non_renewable_energy
			nuclear -= newRow.nuclear
		} else {
			Scope_3 += newRow.scope3
			kwh += newRow.kwh
			renewable_energy += newRow.renewable
			non_renewable_energy += newRow.non_renewable
			nuclear += newRow.nuclear
		}

		setScope(3)

		await updateData({
			Scope_3: parseFloat(Number(Scope_3).toFixed(2)),
			kwh: parseFloat(Number(kwh).toFixed(2)),
			renewable_energy: parseFloat(Number(renewable_energy).toFixed(2)),
			non_renewable_energy: parseFloat(Number(non_renewable_energy).toFixed(2)),
			nuclear: parseFloat(Number(nuclear).toFixed(2)),
			Scope_3_Category: 8,
			scope3Category8Rows: updatedCategory8Rows,
		})
	}

	const updateScope3Emission = async (newRow, isDeleted) => {
		let { Scope_3 } = data // Destructure Scope_3 from data

		let { scope3, Scope_3_Category } = newRow
		console.log(typeof Scope_3)

		if (isDeleted) {
			Scope_3 -= Number(scope3) || 0 // Subtract scope3 from the row
		} else {
			Scope_3 += Number(scope3) || 0 // Add scope3 from the row
		}

		setScope(3) // Set scope to 3

		await updateData({ Scope_3: parseFloat(Number(Scope_3).toFixed(2)), Scope_3_Category: Number(Scope_3_Category) })
	}

	// Determine active state based on rows
	const isCat1Active = scope3Category1BasicRows.length > 0 || scope3Category1MaterialRows.length > 0
	const isCat2Active = scope3Category2Rows.length > 0
	// const isCat3Active = false; // No rows for Cat 3 currently
	const isCat4Active = scope3Category4Rows.length > 0 || scope3Category4VTRows.length > 0
	const isCat5Active = scope3Category5Rows.length > 0
	const isCat6Active =
		scope3Category6AccommodationRows.length > 0 ||
		scope3Category6FlightRows.length > 0 ||
		scope3Category6LandTransportRows.length > 0
	const isCat7Active = scope3Category7Rows.length > 0
	const isCat8Active = scope3Category8Rows.length > 0

	const isAnyCategoryActive =
		isCat1Active || isCat2Active || isCat4Active || isCat5Active || isCat6Active || isCat7Active || isCat8Active

	return (
		<div className='[&>:not(:first-child)]:border-t-2'>
			<DisclosureComponent
				title={t("Category1-PurchasedGoodsAndServices")}
				disabled={isEmploeeCommutingTransaction ? true : isAnyCategoryActive && !isCat1Active}
			>
				<TabGroup>
					<TabList className='flex gap-4 border-b-2 border-sky-500'>
						<Tab className='py-1 px-3 rounded-tr-lg text-sm/6 font-semibold text-gray-500 data-[selected]:text-white data-[selected]:bg-sky-500  data-[selected]:outline-none'>
							{t("BasicInput")}
						</Tab>

						<Tab className='py-1 px-3 rounded-tr-lg rounded-tl-lg text-sm/6 font-semibold text-gray-500 data-[selected]:text-white data-[selected]:bg-sky-500  data-[selected]:outline-none'>
							{t("Material")}
						</Tab>
					</TabList>
					<TabPanels className='bg-white mt-5'>
						<TabPanel className='rounded-xl bg-white/5'>
							<BasicRowContent
								category1BasicRows={scope3Category1BasicRows}
								updateCategory1BasicEmission={updateScope3Emission}
							/>
						</TabPanel>

						<TabPanel className='rounded-xl bg-white/5'>
							<MaterialRowContent
								category1MaterialRows={scope3Category1MaterialRows}
								updateCategory1MaterialEmission={updateScope3Emission}
							/>
						</TabPanel>
					</TabPanels>
				</TabGroup>
			</DisclosureComponent>
			<DisclosureComponent
				title={t("Category2-CapitalGoods")}
				disabled={isEmploeeCommutingTransaction ? true : isAnyCategoryActive && !isCat2Active}
			>
				<Scope3Category2Content category2Rows={scope3Category2Rows} updateCategory2Emission={updateScope3Emission} />
			</DisclosureComponent>
			<DisclosureComponent
				title={t("Category3-FuelAndEnergyRelatedActivities")}
				disabled={isEmploeeCommutingTransaction ? true : isAnyCategoryActive}
			>
				<Category3Content />
			</DisclosureComponent>
			<DisclosureComponent
				title={t("Category4-UpstreamTransportationAndDistribution")}
				disabled={isEmploeeCommutingTransaction ? true : isAnyCategoryActive && !isCat4Active}
			>
				{/* Replace placeholder with Tabs */}
				<TabGroup>
					<TabList className='flex gap-4 border-b-2 border-sky-500'>
						<Tab className='py-1 px-3 rounded-tr-lg text-sm/6 font-semibold text-gray-500 data-[selected]:text-white data-[selected]:bg-sky-500  data-[selected]:outline-none'>
							{t("Default")}
						</Tab>
						<Tab className='py-1 px-3 rounded-tr-lg rounded-tl-lg text-sm/6 font-semibold text-gray-500 data-[selected]:text-white data-[selected]:bg-sky-500  data-[selected]:outline-none'>
							{t("VehicleType")}
						</Tab>
					</TabList>
					<TabPanels className='bg-white mt-5'>
						<TabPanel className='rounded-xl bg-white/5'>
							<Scope3Category4Content
								category4Rows={scope3Category4Rows}
								updateCategory4Emission={updateScope3Emission}
								// category4Factors={/* Pass factors if needed */}
							/>
						</TabPanel>
						<TabPanel className='rounded-xl bg-white/5'>
							<Scope3Category4VTContent
								category4VTRows={scope3Category4VTRows}
								updateCategory4VTEmission={updateCategory4VTEmission}
								PeriodYear={data.PeriodYear}
								// VEHICLE_TYPE_LIST={/* Pass list */}
								// FUEL_TYPE_LIST={/* Pass list */}
							/>
						</TabPanel>
					</TabPanels>
				</TabGroup>
			</DisclosureComponent>
			<DisclosureComponent
				title={t("Category5-WasteGeneratedInOperations")}
				disabled={isEmploeeCommutingTransaction ? true : isAnyCategoryActive && !isCat5Active}
			>
				<Scope3Category5Content
					category5Rows={scope3Category5Rows}
					updateCategory5Emission={updateScope3Emission}
					wasteFactors={[]} // Pass empty array as requested
				/>
			</DisclosureComponent>
			<DisclosureComponent
				title={t("Category6-BusinessTravel")}
				disabled={isEmploeeCommutingTransaction ? true : isAnyCategoryActive && !isCat6Active}
			>
				<TabGroup>
					<TabList className='flex gap-4 border-b-2 border-sky-500'>
						<Tab className='py-1 px-3 rounded-tr-lg text-sm/6 font-semibold text-gray-500 data-[selected]:text-white data-[selected]:bg-sky-500  data-[selected]:outline-none'>
							{t("Flights")}
						</Tab>

						<Tab className='py-1 px-3 rounded-tr-lg rounded-tl-lg text-sm/6 font-semibold text-gray-500 data-[selected]:text-white data-[selected]:bg-sky-500  data-[selected]:outline-none'>
							{t("LandTransport")}
						</Tab>

						<Tab className='py-1 px-3 rounded-tr-lg rounded-tl-lg text-sm/6 font-semibold text-gray-500 data-[selected]:text-white data-[selected]:bg-sky-500  data-[selected]:outline-none'>
							{t("Accommodation")}
						</Tab>
					</TabList>
					<TabPanels className='bg-white mt-2'>
						<TabPanel className='rounded-xl bg-white/5'>
							<Scope3Category6Flights
								flightRows={scope3Category6FlightRows}
								showLoader={showLoader}
								hideLoader={hideLoader}
								updateFloghtEmission={updateScope3Emission}
							/>
						</TabPanel>

						<TabPanel className='rounded-xl bg-white/5'>
							<Scope3Category6LT
								landTransportRows={scope3Category6LandTransportRows}
								updateCategory6LT={updateScope3Emission}
							/>
						</TabPanel>

						<TabPanel className='rounded-xl bg-white/5'>
							<Scope3Category6Accomudation
								accommodationRows={scope3Category6AccommodationRows}
								updateAccommodationEmission={updateScope3Emission}
							/>
						</TabPanel>
					</TabPanels>
				</TabGroup>
			</DisclosureComponent>
			<DisclosureComponent
				title={t("Category7-EmployeeCommuting")}
				isOpen={isEmploeeCommutingTransaction}
				disabled={isEmploeeCommutingTransaction ? false : isAnyCategoryActive && !isCat7Active}
			>
				<Scope3Category7Content category7Rows={scope3Category7Rows} updateCategory7Content={updateScope3Emission} />
			</DisclosureComponent>
			<DisclosureComponent
				title={t("Category8-UpstreamLeasedAssets")}
				disabled={isEmploeeCommutingTransaction ? true : isAnyCategoryActive && !isCat8Active}
			>
				<Scope3Category8Content
					category8Rows={scope3Category8Rows}
					updateCategory8Emission={updateCategory8Emission}
					PeriodYear={data.PeriodYear}
				/>
			</DisclosureComponent>
		</div>
	)
}

export default Scope3Disclosures
