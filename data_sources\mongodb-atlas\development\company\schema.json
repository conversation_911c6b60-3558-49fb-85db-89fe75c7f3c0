{"properties": {"Accounts": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"AccountCreationDate": {"bsonType": "string"}, "AccountDescription": {"bsonType": "string"}, "AccountID": {"bsonType": "string"}, "AccountType": {"bsonType": "string"}, "ClosingCreditBalance": {"bsonType": "string"}, "ClosingDebitBalance": {"bsonType": "string"}, "OpeningCreditBalance": {"bsonType": "string"}, "OpeningDebitBalance": {"bsonType": "string"}, "StandardAccountID": {"bsonType": "string"}}}}, "Address": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"AddressType": {"bsonType": "string"}, "City": {"bsonType": "string"}, "Country": {"bsonType": "string"}, "PostalCode": {"bsonType": "string"}, "StreetName": {"bsonType": "string"}}}}, "Analysis": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"AnalysisID": {"bsonType": "string"}, "AnalysisIDDescription": {"bsonType": "string"}, "AnalysisType": {"bsonType": "string"}, "AnalysisTypeDescription": {"bsonType": "string"}}}}, "AuthId": {"bsonType": "string"}, "ContactPerson": {"bsonType": "object", "properties": {"FirstName": {"bsonType": "string"}, "LastName": {"bsonType": "string"}}}, "Industry": {"bsonType": "string"}, "Logo": {"bsonType": "string"}, "NaceCode": {"bsonType": "string"}, "Name": {"bsonType": "string"}, "OrganizationId": {"bsonType": "string"}, "RegistrationNumber": {"bsonType": "string"}, "Subscription": {"bsonType": "int"}, "Suppliers": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"Address": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"AddressType": {"bsonType": "string"}, "City": {"bsonType": "string", "default": ""}, "Country": {"bsonType": "string"}, "Number": {"bsonType": "string"}, "PostalCode": {"bsonType": "string"}, "StreetName": {"bsonType": ["string", "null"]}}}}, "Amount": {"bsonType": "number"}, "ClosingCreditBalance": {"bsonType": "string"}, "Contact": {"bsonType": "object", "properties": {"ContactPerson": {"bsonType": "object", "properties": {"FirstName": {"bsonType": "string"}, "Initials": {"bsonType": "string"}, "LastName": {"bsonType": "string"}, "OtherTitles": {"bsonType": "string"}, "Salutation": {"bsonType": "string"}}}, "Email": {"bsonType": ["string", "null"]}, "Fax": {"bsonType": "string"}, "Telephone": {"bsonType": "string"}, "Website": {"bsonType": "string"}}}, "Emission": {"bsonType": "number"}, "Industry": {"bsonType": "string"}, "IndustryCo2Intensity": {"oneOf": [{"minimum": {"$numberInt": "0"}, "type": "number"}, {"additionalProperties": false, "patternProperties": {"^[0-9]{4}$": {"type": "number"}}, "type": "object"}]}, "NaceCode": {"bsonType": "string"}, "NaicsCode": {"bsonType": "string"}, "Name": {"bsonType": "string"}, "Notes": {"bsonType": "string"}, "RegistrationNumber": {"bsonType": "string"}, "Status": {"bsonType": "int"}, "SupplierCo2Intensity": {"oneOf": [{"minimum": {"$numberInt": "0"}, "type": "number"}, {"additionalProperties": false, "patternProperties": {"^[0-9]{4}$": {"type": "number"}}, "type": "object"}]}, "SupplierID": {"bsonType": "string"}, "isSupplierFactorUpdated": {"bsonType": "bool", "default": false}}}}, "Updated": {"bsonType": "bool", "default": false}, "_id": {"bsonType": "objectId"}, "emissions": {"bsonType": "object", "patternProperties": {"^[0-9]{4}$": {"bsonType": "object", "properties": {"Scope1": {"bsonType": "number"}, "Scope2": {"bsonType": "number"}, "Scope3": {"bsonType": "number"}}, "required": ["Scope1", "Scope2", "Scope3"]}}, "additionalProperties": false}, "reports": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"ENERGY": {"bsonType": "string"}, "MESSAGE_FROM_CEO": {"bsonType": "string"}, "Metodikk": {"bsonType": "string"}, "OUR_CLIMATE_COMMITMENT": {"bsonType": "string"}, "Referanser": {"bsonType": "string"}, "SCOPE1_EMISSION": {"bsonType": "string"}, "SCOPE2_EMISSION": {"bsonType": "string"}, "SCOPE3_EMISSION": {"bsonType": "string"}, "TOTAL_CEO2_EMISSION": {"bsonType": "string"}, "c1_goods_and_services": {"bsonType": "string"}, "c2_capital_goods": {"bsonType": "string"}, "c3_feul_and_energy": {"bsonType": "string"}, "c4_upstream_transformation": {"bsonType": "string"}, "c5_waste_generation": {"bsonType": "string"}, "c6_business_travel": {"bsonType": "string"}, "c7_employee_commute": {"bsonType": "string"}, "c8_upstream_leased_assets": {"bsonType": "string"}, "c9_15_downstream_emissions": {"bsonType": "string"}, "ceoPicLink": {"bsonType": "string"}, "logo": {"bsonType": "string"}, "suppliersInfo": {"bsonType": "string"}, "year": {"bsonType": "string"}}}}}, "title": "company"}