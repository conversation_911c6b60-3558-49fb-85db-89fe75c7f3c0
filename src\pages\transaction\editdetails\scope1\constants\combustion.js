export const METRIC_LIST_COMBUSTION = [
	{ name: "Petrol bio-blend", value: "PetrolForecourt", label: "Petrol bio-blend" },
	{ name: "Diesel bio-blend", value: "DieselForecourt", label: "Diesel bio-blend" },
	{ name: "Heating/Fuel Oil", value: "FuelOil", label: "Heating/Fuel Oil" },
	{ name: "Compressed natural gas", value: "CNG", label: "Compressed natural gas" },
	{ name: "Natural gas", value: "NaturalGas", label: "Natural gas" },
	{ name: "LPG / Propane", value: "LNG", label: "LPG / Propane" },
	{ name: "Coal", value: "CoalIndustrial", label: "Coal" },
	{ name: "HVO100", value: "HVO100", label: "HVO100" },
]

export const FACTORS_DATA_COMBUSTION = {
	Butane: {
		name: "Butane",
		unit: "litres",
		scope1: 1.74529,
		scope3: 0.19686,
		kwh: 7.711728,
	},
	CNG: {
		name: "Compressed Natural Gas",
		unit: "litres",
		scope1: 0.44423,
		scope3: 0.09487,
		kwh: 2.5,
	},
	LNG: {
		name: "Liquefied Natural Gas",
		unit: "litres",
		scope1: 1.15623,
		scope3: 0.39925,
		kwh: 6.25,
	},
	LPG: {
		name: "Liquefied Petroleum Gas",
		unit: "litres",
		scope1: 1.55709,
		scope3: 0.18383,
		kwh: 7.445,
	},
	NaturalGas: {
		name: "Natural gas",
		unit: "cubic/m3",
		scope1: 2.02135,
		scope3: 0.34593,
		kwh: 11.02,
	},
	NaturalGas100: {
		name: "Natural gas (100% mineral blend)",
		unit: "cubic/m3",
		scope1: 2.03473,
		scope3: 0.34593,
		kwh: 11.02,
	},
	OtherPetroleumGas: {
		name: "Other petroleum gas",
		unit: "litres",
		scope1: 0.94441,
		scope3: 0.11154,
		kwh: 4.2,
	},
	Propane: {
		name: "Propane",
		unit: "litres",
		scope1: 1.54354,
		scope3: 0.18046,
		kwh: 7.445,
	},
	AviationSpirit: {
		name: "Aviation spirit",
		unit: "litres",
		scope1: 2.33048,
		scope3: 0.59512,
		kwh: 9.306,
	},
	AviationTurbineFuel: {
		name: "Aviation turbine fuel",
		unit: "litres",
		scope1: 2.54514,
		scope3: 0.52686,
		kwh: 9.75,
	},
	BurningOil: {
		name: "Burning oil",
		unit: "litres",
		scope1: 2.54014,
		scope3: 0.52807,
		kwh: 10.31,
	},
	DieselForecourt: {
		name: "Diesel (average biofuel blend)",
		unit: "litres",
		scope1: 2.51233,
		scope3: 0.60986,
		kwh: 10.96,
	},
	Diesel100: {
		name: "Diesel (100% mineral diesel)",
		unit: "litres",
		scope1: 2.70553,
		scope3: 0.62874,
		kwh: 10.96,
	},
	FuelOil: {
		name: "Fuel oil",
		unit: "litres",
		scope1: 3.17522,
		scope3: 0.69723,
		kwh: 11.84,
	},
	GasOil: {
		name: "Gas oil (red diesel)",
		unit: "litres",
		scope1: 2.75857,
		scope3: 0.63253,
		kwh: 10.96,
	},
	Lubricants: {
		name: "Lubricants",
		unit: "litres",
		scope1: 2.74972,
		scope3: 0.712227636,
		kwh: 10.96,
	},
	Naphtha: {
		name: "Naphtha",
		unit: "litres",
		scope1: 2.11926,
		scope3: 0.43210227,
		kwh: 9.85,
	},
	PetrolForecourt: {
		name: "Petrol (average biofuel blend)",
		unit: "litres",
		scope1: 2.19352,
		scope3: 0.61328,
		kwh: 9.61,
	},
	Petrol100: {
		name: "Petrol (100% mineral petrol)",
		unit: "litres",
		scope1: 2.33969,
		scope3: 0.60283,
		kwh: 9.61,
	},
	ProcessedResidual: {
		name: "Processed fuel oils - residual oil",
		unit: "litres",
		scope1: 3.17522,
		scope3: 0.82185,
		kwh: 10.53,
	},
	ProcessedDistillate: {
		name: "Processed fuel oils - distillate oil",
		unit: "litres",
		scope1: 2.75857,
		scope3: 0.70791,
		kwh: 10.53,
	},
	RefineryMisc: {
		name: "Refinery miscellaneous",
		unit: "kg",
		scope1: 2.94482,
		scope3: 0.34679114,
		kwh: 10.34,
	},
	WasteOil: {
		name: "Waste oils",
		unit: "litres",
		scope1: 2.75368,
		scope3: 0,
		kwh: 10.53,
	},
	HVO100: {
		name: "HVO100",
		unit: "litres",
		scope1: 0.03558,
		scope3: 0.2132,
		kwh: 9.93,
	},
	MarineGasOil: {
		name: "Marine gas oil",
		unit: "litres",
		scope1: 2.77539,
		scope3: 0.63253,
		kwh: 10.53,
	},
	MarineFuelOil: {
		name: "Marine fuel oil",
		unit: "litres",
		scope1: 3.10669,
		scope3: 0.69723,
		kwh: 10.53,
	},
	Fuel: {
		name: "Fuel",
		unit: "kg",
		scope1: 2.40384,
		scope3: 0.39314029,
		kwh: 9.89,
	},
	CoalIndustrial: {
		name: "Coal (industrial)",
		unit: "kg",
		scope1: 2.40384,
		scope3: 0.39314029,
		kwh: 7.25,
	},
	"Coal (electricity generation)": {
		name: "Coal (electricity generation)",
		unit: "kg",
		scope1: 2.25234,
		scope3: 0.37227789,
		kwh: 7.25,
	},
	"Coal (domestic)": {
		name: "Coal (domestic)",
		unit: "kg",
		scope1: 2.88326,
		scope3: 0.4427895,
		kwh: 7.25,
	},
	"Coking coal": {
		name: "Coking coal",
		unit: "kg",
		scope1: 3.16524,
		scope3: 0.46796718,
		kwh: 8.33,
	},
	"Petroleum coke": {
		name: "Petroleum coke",
		unit: "kg",
		scope1: 3.38686,
		scope3: 0.39924907,
		kwh: 9.85,
	},
}
