/* eslint-disable import/no-unresolved */
import React, { useState, useRef } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"
import Select from "react-select"

import { CATEGORY4_DATA as category4Factors } from "../contants/category4"

import Button from "@/components/ui/Button" // Assuming alias setup
import Input from "@/components/ui/Input" // Assuming alias setup
import { useToast } from "@/hooks"

function Scope3Category4Content(props) {
	const { category4Rows = [], setCategory4Rows, updateCategory4Emission } = props
	const { t } = useTranslation("transactions")
	const weightInputRef = useRef()
	const toast = useToast()

	// Local state for the "Add New" form inputs
	const [transportType, setTransportType] = useState(category4Factors[0]) // Selected option object
	const [factor, setFactor] = useState(category4Factors[0].co2e)
	const [factorUnit, setFactorUnit] = useState("g Co2e") // Default or derived from selection
	const [weight, setWeight] = useState(0.0)
	const [weightUnit, setWeightUnit] = useState("kgs") // Default or derived
	const [distance, setDistance] = useState(0.0)
	const [distanceUnit, setDistanceUnit] = useState("kms") // Default or derived

	// Handle Transport Type selection
	const handleTransportTypeChange = (selectedOption) => {
		setTransportType(selectedOption)
		// Assuming selectedOption has co2e, factorUnit, weightUnit, distanceUnit properties
		// The original component multiplies co2e by 1000, let's keep factor as is from data
		setFactor(selectedOption?.co2e || 0.0)
		setFactorUnit(selectedOption?.factorUnit || "g Co2e") // Adjust based on actual data structure
		setWeightUnit(selectedOption?.weightUnit || "kgs")
		setDistanceUnit(selectedOption?.distanceUnit || "kms")
		weightInputRef.current?.focus()
	}

	// Adds the new row data (from local state) to the main list (parent state)
	const addCategory4Row = (calculatedScope3) => {
		const newCategory4Row = {
			transportType: transportType, // Store the selected option object
			factor: Number(factor),
			factorUnit: factorUnit,
			weight: Number(weight),
			weightUnit: weightUnit,
			distance: Number(distance),
			distanceUnit: distanceUnit,
			Scope_3_Category: 4,
			scope1: 0,
			scope2: 0,
			scope3: Number(calculatedScope3), // Calculated emission
		}

		const newCategory4RowsList = [...category4Rows, newCategory4Row]
		setCategory4Rows(newCategory4RowsList)

		// Optionally notify parent about the addition
		if (updateCategory4Emission) {
			updateCategory4Emission(newCategory4Row, false) // false indicates addition
		}

		// Reset form fields
		setTransportType(category4Factors[0])
		setFactor(category4Factors[0].co2e)
		setFactorUnit("g Co2e")
		setWeight(0.0)
		setWeightUnit("kgs")
		setDistance(0.0)
		setDistanceUnit("kms")
	}

	// Calculates emission and triggers adding the row
	const handleAddEmission = () => {
		// Basic validation
		if (!transportType) {
			toast("error", t("transport_type_error"))
			return
		}

		if (parseFloat(factor) <= 0 && parseFloat(weight) <= 0 && parseFloat(distance) <= 0) {
			toast("error", t("factor_weight_distance_greater_0_error"))
			return
		}

		const numFactor = parseFloat(factor)
		const numWeight = parseFloat(weight)
		const numDistance = parseFloat(distance)

		let calculatedScope3 = (numWeight / 1000) * (numFactor / 1000) * numDistance
		addCategory4Row(calculatedScope3)
	}

	// Deletes a row from the list by index
	const deleteCategory4Row = (index) => {
		const rowToDelete = category4Rows[index]
		const newCategory4RowsList = [...category4Rows]
		newCategory4RowsList.splice(index, 1)

		setCategory4Rows(newCategory4RowsList)

		// Optionally notify parent about the deletion
		if (updateCategory4Emission) {
			updateCategory4Emission(rowToDelete, true) // true indicates deletion
		}
	}

	const handleFocus = (event) => event.target.select()

	return (
		<>
			{/* Display existing/saved rows */}
			{category4Rows.map((category4Item, index) => (
				// Using the display structure from the original component's 'isSaved' block
				<div key={index}>
					<div className='grid grid-cols-6 gap-8 my-1 saved-emission'>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("TransportType")}</label>
							{/* Assuming transportType is stored as an object { value, label } */}
							<div>{category4Item.transportType?.label || "N/A"}</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Factor")}</label>
							<div className='flex relative'>
								<span>{Number(category4Item.factor).toFixed(2)}</span>
								<span className='text-nowrap custom-span-unit-value-save'>
									{"g Co2e"} {/* Adjust unit display as needed */}
								</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Weight")}</label>
							<div className='flex relative'>
								<span>{category4Item.weight}</span>
								<span className='text-nowrap custom-span-unit-value-save'>{category4Item.weightUnit || "kgs"}</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Distance")}</label>
							<div className='flex relative'>
								<span>{category4Item.distance}</span>
								<span className='text-nowrap custom-span-unit-value-save'>{category4Item.distanceUnit || "kms"}</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("TotalCo2e")}</label>
							<div className='flex relative'>
								<span>
									{Number(
										(category4Item.scope1 || 0) + (category4Item.scope2 || 0) + (category4Item.scope3 || 0)
									).toFixed(2)}
								</span>
								<span className='text-nowrap custom-span-unit-value-save'>{t("kg")}</span>
							</div>
						</div>
						<div className='!items-center delete-icon'>
							<span aria-hidden onClick={() => deleteCategory4Row(index)} style={{ cursor: "pointer" }}>
								<RiDeleteBin6Line />
							</span>
						</div>
					</div>
					<hr />
				</div>
			))}

			{/* Form for adding new rows */}
			<div className='grid grid-cols-5 my-2 gap-3'>
				<div>
					<label className='text-slate-800 whitespace-nowrap font-semibold' htmlFor={"c4-transport-new"}>
						{t("TransportType")}
					</label>
					<Select
						id={"c4-transport-new"}
						value={transportType}
						onChange={handleTransportTypeChange}
						options={category4Factors}
						placeholder={t("Select transport type...")}
						maxMenuHeight={150}
					/>
				</div>
				<div>
					{/* Factor is now derived from selection, display read-only or hide */}
					<Input
						label={t("Factor")}
						value={Number(factor).toFixed(2)}
						labelColor='text-slate-500' // Indicate read-only/derived
						readOnly // Make it read-only
						unit={`${factorUnit}/${t("tkm")}`}
					/>
				</div>
				<div>
					<Input
						label={t("Weight")}
						placeholder={t("Enter weight")}
						value={weight}
						labelColor='text-sky-500'
						handleChange={(e) => setWeight(e.target.value)}
						type='number'
						ref={weightInputRef}
						handleFocus={handleFocus}
						unit={weightUnit}
						min='0'
						step='any'
					/>
				</div>
				<div>
					<Input
						label={t("Distance")}
						placeholder={t("Enter distance")}
						value={distance}
						labelColor='text-sky-500'
						handleChange={(e) => setDistance(e.target.value)}
						type='number'
						handleFocus={handleFocus}
						unit={distanceUnit}
						min='0'
						step='any'
					/>
				</div>
				<div className='self-end mb-1'>
					<Button title={`${t("Add")} | +`} handleClick={handleAddEmission} color='sky-500' />
				</div>
			</div>
		</>
	)
}

export default Scope3Category4Content
