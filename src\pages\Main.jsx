import { useUser, useClerk } from "@clerk/clerk-react"
import React, { useEffect } from "react"
import { useDispatch } from "react-redux"
import { useNavigate } from "react-router-dom"

import { useRealmApp } from "../realm/RealmAppProvider"
import { currentOrganizationAction, isCompanyRegistered } from "../store/actions/UserAction"

import useFullPageLoader from "./loader/useFullPageLoader"

const Main = () => {
	const app = useRealmApp()
	const navigate = useNavigate()
	const [loader, showLoader] = useFullPageLoader()
	const { user } = useUser()
	const { signOut } = useClerk()
	//const { getOrganizationMemberships } = useOrganizationList()
	const dispatch = useDispatch()
	useEffect(() => {
		const checkingUser = async () => {
			/* eslint react-hooks/exhaustive-deps: 0 */
			showLoader()
			if (user) {
				// Get user data form user collection(user.id = netlifyId)
				/* eslint react-hooks/exhaustive-deps: 0 */
				const realmUser = await app.getRealmUserData(user.id)
				// Check user uploaded saf-t file, yes = redirect to dashboard, no = redirect to welcome page
				if (realmUser?.RegistrationNumber) {
					/* eslint react-hooks/exhaustive-deps: 0 */
					dispatch(
						currentOrganizationAction({
							CompanyName: realmUser.CompanyName,
							RegistrationNumber: realmUser.RegistrationNumber,
							company: realmUser.companyInfo,
							email: realmUser.email,
							name: realmUser.name,
							netlifyID: realmUser.netlifyID,
							organizationId: realmUser.organizationId,
							Subscription: realmUser.Subscription,
							organizationCreated: realmUser.organizationId ? true : false,
						})
					)
					dispatch(isCompanyRegistered(true))
					return navigate("/dashboard")
				} else {
					const { data } = await user.getOrganizationMemberships()
					if (data && data.length) {
						try {
							let res = await app.getRealmUserByOrgId(data[0].organization.id)
							if (res.RegistrationNumber)
								dispatch(
									currentOrganizationAction({
										CompanyName: res.CompanyName,
										RegistrationNumber: res.RegistrationNumber,
										company: res.companyInfo,
										email: res.email,
										name: res.name,
										netlifyID: res.netlifyID,
										organizationId: res.organizationId,
										Subscription: res.Subscription,
										organizationCreated: res.organizationId ? true : false,
									})
								)
							/* eslint react-hooks/exhaustive-deps: 0 */
							dispatch(isCompanyRegistered(true))
							return navigate("/dashboard")
						} catch (error) {
							/* eslint react-hooks/exhaustive-deps: 0 */
							return navigate("/welcome")
						}
					} else {
						/* eslint react-hooks/exhaustive-deps: 0 */
						return navigate("/welcome")
					}
				}
			} else {
				signOut()
			}
		}

		checkingUser()
	}, [])

	return <div>{loader}</div>
}

export default Main
