import useGetCO2AddedData from "./dashboard/useGetCO2AddedData"
import useGetCompanyEmission from "./dashboard/useGetCompanyEmission"
import useGetIndustryCo2Intensity from "./dashboard/useGetIndustryCo2Intensity"
import useGetMonthlyChartData from "./dashboard/useGetMonthlyChartData"
import useGetScopeCategoryData from "./dashboard/useGetScopeCategoryData"
import useGetScopeChartData from "./dashboard/useGetScopeChartData"
import useGetTopSuppliers from "./dashboard/useGetTopSuppliers"
import useGetYearlyChartData from "./dashboard/useGetYearlyChartData"
import useGetLastUploadedTransactionDate from "./setting/useGetLastUploadedTransactionDate"
import useGetLockStatus from "./setting/useGetLockStatus"
import useGetPublishedDataStatus from "./setting/useGetPublishedDataStatus"
import useGetSuppliers from "./suppliers/useGetSuppliers"
import useToast from "./toast/useToast"
// TRANSACTIONS
import useGetAirportData from "./transactions/useGetAirportData"
import useGetCategory5FactorApiData from "./transactions/useGetCategory5FactorApiData"
//import useGetFugitiveApiData from "./transactions/useGetFugitiveApiData"
import useGetNaceQuery from "./transactions/useGetNace"
import useGetSuppliersList from "./transactions/useGetSuppliersList"
import useGetTransactions from "./transactions/useGetTransactions"

// SUPPLIERS

// SETTINGS

// DASHBOARD

export {
	useToast,
	useGetTransactions,
	useGetAirportData,
	useGetCategory5FactorApiData,
	useGetNaceQuery,
	useGetSuppliersList,
	useGetSuppliers,
	useGetLockStatus,
	useGetLastUploadedTransactionDate,
	useGetPublishedDataStatus,
	useGetCO2AddedData,
	useGetCompanyEmission,
	useGetMonthlyChartData,
	useGetScopeCategoryData,
	useGetScopeChartData,
	useGetTopSuppliers,
	useGetYearlyChartData,
	useGetIndustryCo2Intensity,
}
